import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Bed, Users } from "lucide-react";
import { Tables } from "@/integrations/supabase/types";
import ImageUploader from "@/components/ui/ImageUploader";
import { deleteRoomTypeImages } from "@/lib/storage-utils";

type RoomType = Tables<"room_types">;

interface RoomTypeManagerProps {
  hotelId: string;
}

const roomTypeSchema = z.object({
  name: z.string().min(1, "Room type name is required"),
  description: z.string().min(1, "Description is required"),
  max_occupancy: z.number().min(1, "Max occupancy must be at least 1"),
  bed_configuration: z.string().min(1, "Bed configuration is required"),
  room_size_sqm: z.string().optional(),
  base_price: z.number().min(1, "Price must be greater than 0"),
  amenities: z.array(z.string()).default([]),
});

type RoomTypeFormData = z.infer<typeof roomTypeSchema>;

const ROOM_AMENITIES = [
  "Air Conditioning",
  "Heating",
  "Free Wi-Fi",
  "TV",
  "Cable/Satellite TV",
  "Minibar",
  "Coffee/Tea Maker",
  "Safe",
  "Hair Dryer",
  "Bathrobe",
  "Slippers",
  "Toiletries",
  "Balcony",
  "Ocean View",
  "City View",
  "Mountain View",
  "Garden View",
  "Soundproof",
  "Non-Smoking",
  "Connecting Rooms",
  "Kitchenette",
  "Refrigerator",
  "Microwave",
  "Dishwasher",
  "Washing Machine",
  "Iron/Ironing Board",
  "Desk",
  "Seating Area",
  "Sofa Bed",
  "Jacuzzi",
  "Private Bathroom",
  "Shared Bathroom",
  "Shower",
  "Bathtub",
  "Bidet",
  "Wheelchair Accessible",
];

const RoomTypeManager = ({ hotelId }: RoomTypeManagerProps) => {
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRoomType, setEditingRoomType] = useState<RoomType | null>(null);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(0);
  const [customAmenity, setCustomAmenity] = useState("");
  const [customAmenities, setCustomAmenities] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [roomTypeToDelete, setRoomTypeToDelete] = useState<string | null>(null);

  const form = useForm<RoomTypeFormData>({
    resolver: zodResolver(roomTypeSchema),
    defaultValues: {
      name: "",
      description: "",
      max_occupancy: 2,
      bed_configuration: "",
      room_size_sqm: "",
      base_price: 0,
      amenities: [],
    },
  });

  useEffect(() => {
    fetchRoomTypes();
  }, [hotelId]);

  const fetchRoomTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("room_types")
        .select("*")
        .eq("hotel_id", hotelId)
        .order("created_at", { ascending: true });

      if (error) throw error;
      setRoomTypes(data || []);
    } catch (error) {
      toast.error("Failed to fetch room types");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: RoomTypeFormData) => {
    // Additional validation for required fields
    if (selectedImages.length === 0) {
      toast.error("Please upload at least one image of the room type.");
      return;
    }

    if (selectedAmenities.length === 0) {
      toast.error("Please select at least one amenity for the room type.");
      return;
    }

    setIsSubmitting(true);
    try {
      let imageUrls: string[] = [];

      // Upload images if provided
      if (selectedImages.length > 0) {
        const uploadPromises = selectedImages.map(async (file: File) => {
          const fileExt = file.name.split(".").pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `room-types/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from("hotel-images")
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          const {
            data: { publicUrl },
          } = supabase.storage.from("hotel-images").getPublicUrl(filePath);

          return publicUrl;
        });

        imageUrls = await Promise.all(uploadPromises);

        // Reorder images to put thumbnail first
        if (thumbnailIndex > 0 && thumbnailIndex < imageUrls.length) {
          const thumbnailUrl = imageUrls[thumbnailIndex];
          imageUrls.splice(thumbnailIndex, 1);
          imageUrls.unshift(thumbnailUrl);
        }
      }

      const roomTypeData = {
        hotel_id: hotelId,
        name: data.name,
        description: data.description || null,
        max_occupancy: data.max_occupancy,
        bed_configuration: data.bed_configuration || null,
        room_size_sqm: data.room_size_sqm ? parseInt(data.room_size_sqm) : null,
        base_price: data.base_price,
        amenities: selectedAmenities,
        images: imageUrls,
      };

      if (editingRoomType) {
        const { error } = await supabase
          .from("room_types")
          .update(roomTypeData)
          .eq("id", editingRoomType.id);

        if (error) throw error;
        toast.success("Room type updated successfully!");
      } else {
        const { error } = await supabase
          .from("room_types")
          .insert(roomTypeData);

        if (error) throw error;
        toast.success("Room type created successfully!");
      }

      setIsDialogOpen(false);
      setEditingRoomType(null);
      form.reset();
      setSelectedAmenities([]);
      setSelectedImages([]);
      setThumbnailIndex(0);
      setCustomAmenities([]);
      setCustomAmenity("");
      fetchRoomTypes();
    } catch (error) {
      toast.error("Failed to save room type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (roomType: RoomType) => {
    setEditingRoomType(roomType);
    setSelectedAmenities(roomType.amenities || []);

    // Initialize custom amenities from existing amenities
    const customAmens = (roomType.amenities || []).filter(
      (a) => !ROOM_AMENITIES.includes(a)
    );
    setCustomAmenities(customAmens);

    form.reset({
      name: roomType.name,
      description: roomType.description || "",
      max_occupancy: roomType.max_occupancy,
      bed_configuration: roomType.bed_configuration || "",
      room_size_sqm: roomType.room_size_sqm?.toString() || "",
      base_price: roomType.base_price,
      amenities: roomType.amenities || [],
    });

    // Note: For editing, we don't load existing images into the uploader
    // This is intentional - user can add new images or keep existing ones
    setSelectedImages([]);
    setThumbnailIndex(0);

    setIsDialogOpen(true);
  };

  const handleDeleteClick = (roomTypeId: string) => {
    setRoomTypeToDelete(roomTypeId);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!roomTypeToDelete) return;

    try {
      // First, get the room type data to access images
      const { data: roomTypeData, error: fetchError } = await supabase
        .from("room_types")
        .select("images")
        .eq("id", roomTypeToDelete)
        .single();

      if (fetchError) throw fetchError;

      // Delete the room type from database
      const { error: deleteError } = await supabase
        .from("room_types")
        .delete()
        .eq("id", roomTypeToDelete);

      if (deleteError) throw deleteError;

      // Delete associated images from storage
      if (roomTypeData?.images && roomTypeData.images.length > 0) {
        const { failed } = await deleteRoomTypeImages(roomTypeData.images);
        if (failed.length > 0) {
          console.warn(`Failed to delete ${failed.length} images from storage`);
        }
      }

      toast.success("Room type deleted successfully!");
      fetchRoomTypes();
    } catch (error) {
      console.error("Error deleting room type:", error);
      toast.error("Failed to delete room type");
    } finally {
      setDeleteConfirmOpen(false);
      setRoomTypeToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setRoomTypeToDelete(null);
  };

  const handleAmenityToggle = (amenity: string, checked: boolean) => {
    if (checked) {
      setSelectedAmenities([...selectedAmenities, amenity]);
    } else {
      setSelectedAmenities(selectedAmenities.filter((a) => a !== amenity));
    }
  };

  const addCustomAmenity = () => {
    if (
      customAmenity.trim() &&
      !selectedAmenities.includes(customAmenity.trim())
    ) {
      const newAmenity = customAmenity.trim();
      setCustomAmenities([...customAmenities, newAmenity]);
      setSelectedAmenities([...selectedAmenities, newAmenity]);
      setCustomAmenity("");
    }
  };

  const removeCustomAmenity = (amenity: string) => {
    setCustomAmenities(customAmenities.filter((a) => a !== amenity));
    setSelectedAmenities(selectedAmenities.filter((a) => a !== amenity));
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading room types...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Room Types</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingRoomType(null);
                form.reset();
                setSelectedAmenities([]);
                setSelectedImages([]);
                setThumbnailIndex(0);
                setCustomAmenities([]);
                setCustomAmenity("");
              }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Room Type
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingRoomType ? "Edit Room Type" : "Add New Room Type"}
              </DialogTitle>
              <DialogDescription>
                Define the details for this room type including pricing and
                amenities.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Room Type Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Deluxe Ocean View" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe this room type..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="max_occupancy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Max Occupancy</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="base_price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base Price (per night)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bed_configuration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bed Configuration</FormLabel>
                        <FormControl>
                          <Input placeholder="1 King Bed" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="room_size_sqm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Room Size (sqm)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <div>
                    <FormLabel>Room Images</FormLabel>
                    <ImageUploader
                      onImagesChange={setSelectedImages}
                      onThumbnailChange={setThumbnailIndex}
                      maxImages={10}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <FormLabel>Room Amenities</FormLabel>
                  <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded p-3">
                    {ROOM_AMENITIES.map((amenity) => (
                      <label
                        key={amenity}
                        className="flex items-center space-x-2 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedAmenities.includes(amenity)}
                          onChange={(e) =>
                            handleAmenityToggle(amenity, e.target.checked)
                          }
                          className="rounded"
                        />
                        <span className="text-sm">{amenity}</span>
                      </label>
                    ))}
                  </div>

                  {/* Custom Amenities */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Add custom amenity..."
                        value={customAmenity}
                        onChange={(e) => setCustomAmenity(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            addCustomAmenity();
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addCustomAmenity}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>

                    {customAmenities.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {customAmenities.map((amenity) => (
                          <Badge
                            key={amenity}
                            variant="secondary"
                            className="flex items-center gap-1"
                          >
                            {amenity}
                            <button
                              type="button"
                              onClick={() => removeCustomAmenity(amenity)}
                              className="ml-1 hover:text-red-500"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {editingRoomType ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      `${editingRoomType ? "Update" : "Create"} Room Type`
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {roomTypes.map((roomType) => (
          <Card key={roomType.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Bed className="w-5 h-5" />
                    {roomType.name}
                  </CardTitle>
                  <CardDescription>{roomType.description}</CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(roomType)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteClick(roomType.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Room Images */}
              {roomType.images && roomType.images.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-4">
                  <div className="md:col-span-2">
                    <img
                      src={roomType.images[0]}
                      alt={roomType.name}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-1 gap-2">
                    {roomType.images.slice(1, 3).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`${roomType.name} - ${index + 1}`}
                        className="w-full h-16 md:h-[60px] object-cover rounded-lg"
                      />
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">
                    Max {roomType.max_occupancy} guests
                  </span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">${roomType.base_price}</span>{" "}
                  per night
                </div>
                {roomType.bed_configuration && (
                  <div className="text-sm text-gray-600">
                    {roomType.bed_configuration}
                  </div>
                )}
                {roomType.room_size_sqm && (
                  <div className="text-sm text-gray-600">
                    {roomType.room_size_sqm} sqm
                  </div>
                )}
              </div>

              {roomType.amenities && roomType.amenities.length > 0 && (
                <div className="space-y-2">
                  <span className="text-sm font-medium">Amenities:</span>
                  <div className="flex flex-wrap gap-1">
                    {roomType.amenities.map((amenity) => (
                      <Badge
                        key={amenity}
                        variant="secondary"
                        className="text-xs"
                      >
                        {amenity}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {roomTypes.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500 mb-4">No room types defined yet.</p>
              <p className="text-sm text-gray-400">
                Add room types to start accepting bookings for your hotel.
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Room Type</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this room type? This action cannot
              be undone. All associated bookings and data will be permanently
              removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete Room Type
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default RoomTypeManager;
