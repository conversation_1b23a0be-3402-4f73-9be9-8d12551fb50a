import { supabase } from "@/integrations/supabase/client";

/**
 * Extracts the file path from a Supabase storage URL
 * @param url - The full Supabase storage URL
 * @param bucketName - The bucket name to extract path from
 * @returns The file path within the bucket
 */
export function extractFilePathFromUrl(
  url: string,
  bucketName: string
): string | null {
  try {
    // Handle both old and new Supabase URL formats
    const patterns = [
      // New format: https://project.supabase.co/storage/v1/object/public/bucket/path
      new RegExp(`/storage/v1/object/public/${bucketName}/(.+)$`),
      // Old format: https://project.supabase.co/storage/v1/object/sign/bucket/path
      new RegExp(`/storage/v1/object/sign/${bucketName}/(.+)\\?`),
      // Direct bucket path format
      new RegExp(`/${bucketName}/(.+)$`),
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return decodeURIComponent(match[1]);
      }
    }

    // If no pattern matches, try to extract from the end of URL
    const urlParts = url.split("/");
    const bucketIndex = urlParts.indexOf(bucketName);
    if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {
      return urlParts.slice(bucketIndex + 1).join("/");
    }

    return null;
  } catch (error) {
    console.error("Error extracting file path from URL:", error);
    return null;
  }
}

/**
 * Deletes multiple images from Supabase storage
 * @param imageUrls - Array of image URLs to delete
 * @param bucketName - The storage bucket name
 * @returns Promise with deletion results
 */
export async function deleteImagesFromStorage(
  imageUrls: string[],
  bucketName: string
): Promise<{ success: string[]; failed: string[] }> {
  const success: string[] = [];
  const failed: string[] = [];

  if (!imageUrls || imageUrls.length === 0) {
    return { success, failed };
  }

  // Extract file paths from URLs
  const filePaths: string[] = [];
  for (const url of imageUrls) {
    const filePath = extractFilePathFromUrl(url, bucketName);
    if (filePath) {
      filePaths.push(filePath);
    } else {
      console.warn(`Could not extract file path from URL: ${url}`);
      failed.push(url);
    }
  }

  if (filePaths.length === 0) {
    return { success, failed };
  }

  try {
    // Delete files from storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .remove(filePaths);

    if (error) {
      console.error(`Error deleting images from ${bucketName}:`, error);
      failed.push(...imageUrls);
    } else {
      success.push(...imageUrls);
    }
  } catch (error) {
    console.error(`Exception deleting images from ${bucketName}:`, error);
    failed.push(...imageUrls);
  }

  return { success, failed };
}

/**
 * Deletes property images from storage
 * @param imageUrls - Array of property image URLs
 */
export async function deletePropertyImages(imageUrls: string[]) {
  return deleteImagesFromStorage(imageUrls, "property-images");
}

/**
 * Deletes car images from storage
 * @param imageUrls - Array of car image URLs
 */
export async function deleteCarImages(imageUrls: string[]) {
  return deleteImagesFromStorage(imageUrls, "car-images");
}

/**
 * Deletes hotel images from storage
 * @param imageUrls - Array of hotel image URLs
 */
export async function deleteHotelImages(imageUrls: string[]) {
  return deleteImagesFromStorage(imageUrls, "hotel-images");
}

/**
 * Deletes room type images from storage
 * @param imageUrls - Array of room type image URLs
 */
export async function deleteRoomTypeImages(imageUrls: string[]) {
  return deleteImagesFromStorage(imageUrls, "hotel-images");
}
