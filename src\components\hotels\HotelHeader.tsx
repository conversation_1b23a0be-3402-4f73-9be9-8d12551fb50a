import { Map<PERSON><PERSON>, <PERSON>, <PERSON>hare, Heart, ArrowLeft, TestTube } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface HotelHeaderProps {
  title: string;
  location: string;
  rating: number;
  reviews: number;
  is_dummy?: boolean;
}

const HotelHeader = ({
  title,
  location,
  rating,
  reviews,
  is_dummy,
}: HotelHeaderProps) => {
  const handleShare = async () => {
    const url = window.location.href;
    const shareData = {
      title: `${title} - Gesco Stay`,
      text: `Check out this amazing hotel: ${title} in ${location}`,
      url: url,
    };

    try {
      if (navigator.share && navigator.canShare(shareData)) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(url);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      try {
        await navigator.clipboard.writeText(url);
        toast.success("Link copied to clipboard!");
      } catch (clipboardError) {
        toast.error("Failed to share or copy link");
      }
    }
  };

  return (
    <div className="mb-6 relative">
      {is_dummy && (
        <div className="absolute top-0 right-0 z-10">
          <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1">
            <TestTube className="w-3 h-3" />
            Display Only
          </Badge>
        </div>
      )}

      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{title}</h1>
          <div className="flex items-center text-gray-600 mb-2">
            <MapPin className="w-5 h-5 mr-2" />
            <span>{location}</span>
          </div>

          {/* Rating and Reviews - Only show if there are actual reviews */}
          {rating > 0 && reviews > 0 ? (
            <div className="flex items-center mt-1">
              <Star className="w-4 h-4 text-yellow-500 mr-1 fill-current" />
              <span className="font-medium">{rating.toFixed(1)}</span>
              <span className="mx-1">·</span>
              <span className="text-blue-600 hover:underline cursor-pointer">
                {reviews} reviews
              </span>
            </div>
          ) : null}
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share className="w-4 h-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Heart className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HotelHeader;
