-- Add is_active column to all listing tables for controlling visibility
-- This allows hosts to hide/show their listings from public view

-- Add is_active column to properties table
ALTER TABLE public.properties 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add is_active column to cars table
ALTER TABLE public.cars 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add is_active column to hotels table
ALTER TABLE public.hotels 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add indexes for better query performance when filtering by is_active
CREATE INDEX IF NOT EXISTS idx_properties_is_active ON public.properties(is_active);
CREATE INDEX IF NOT EXISTS idx_cars_is_active ON public.cars(is_active);
CREATE INDEX IF NOT EXISTS idx_hotels_is_active ON public.hotels(is_active);

-- Add composite indexes for common query patterns (status + is_active)
CREATE INDEX IF NOT EXISTS idx_properties_status_active ON public.properties(status, is_active);
CREATE INDEX IF NOT EXISTS idx_cars_status_active ON public.cars(status, is_active);
CREATE INDEX IF NOT EXISTS idx_hotels_status_active ON public.hotels(status, is_active);

-- Add comments to document the purpose
COMMENT ON COLUMN public.properties.is_active IS 'Flag to control if listing is visible to public (true = visible, false = hidden)';
COMMENT ON COLUMN public.cars.is_active IS 'Flag to control if listing is visible to public (true = visible, false = hidden)';
COMMENT ON COLUMN public.hotels.is_active IS 'Flag to control if listing is visible to public (true = visible, false = hidden)';
