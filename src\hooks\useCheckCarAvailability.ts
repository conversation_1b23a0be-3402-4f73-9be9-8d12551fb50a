
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const useCheckCarAvailability = (
  carId: string | undefined,
  startDate: Date | undefined,
  endDate: Date | undefined
) => {
  return useQuery({
    queryKey: ["car-availability", carId, startDate, endDate],
    queryFn: async () => {
      if (!carId || !startDate || !endDate)
        return { available: false, message: "Missing required information" };

      // Format dates as strings
      const startStr = startDate.toISOString().split("T")[0];
      const endStr = endDate.toISOString().split("T")[0];


      const { data, error } = await supabase.rpc("check_car_availability", {
        car_id: carId,
        start_date: startStr,
        end_date: endStr,
      });

      if (error) {
        console.error("Error checking car availability:", error);
        throw error;
      }


      return {
        available: data,
        message: data
          ? "Car is available for the selected dates"
          : "Car is not available for the selected dates",
      };
    },
    enabled: !!carId && !!startDate && !!endDate,
  });
};
