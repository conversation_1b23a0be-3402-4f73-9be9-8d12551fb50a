import { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { format } from "date-fns";
import { toast } from "sonner";

type Message = {
  id: string;
  sender_id: string;
  content: string;
  created_at: string;
  read_at: string | null;
};

type Conversation = {
  id: string;
  other_user_id: string;
  other_user_name: string;
  other_user_avatar: string | null;
  property_id: string | null;
  property_title: string | null;
  car_id: string | null;
  car_title: string | null;
  hotel_id: string | null;
  last_message_content: string | null;
  last_message_at: string;
  last_message_sender_id: string | null;
  unread_count: number;
  created_at: string;
};

// Real data will be fetched from Supabase

const HostMessages = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [contacts, setContacts] = useState<Conversation[]>([]);
  const [selectedContact, setSelectedContact] = useState<Conversation | null>(
    null
  );
  const [conversation, setConversation] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  const fetchConversations = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch conversations from Supabase
      const { data: conversationsData, error } = await supabase
        .from("conversations")
        .select(
          `
          id,
          participant_1_id,
          participant_2_id,
          property_id,
          car_id,
          hotel_id,
          created_at,
          updated_at
        `
        )
        .or(`participant_1_id.eq.${user.id},participant_2_id.eq.${user.id}`)
        .order("updated_at", { ascending: false });

      if (error) throw error;

      // Transform conversations to match our Conversation type
      const contactsList: Conversation[] = [];

      if (conversationsData) {
        for (const conv of conversationsData) {
          // Determine the other participant
          const otherUserId =
            conv.participant_1_id === user.id
              ? conv.participant_2_id
              : conv.participant_1_id;

          // Fetch other user's profile
          const { data: profileData } = await supabase
            .from("profiles")
            .select("first_name, last_name, avatar_url")
            .eq("id", otherUserId)
            .single();

          // Fetch property/car/hotel title if applicable
          let listingTitle = null;
          if (conv.property_id) {
            const { data: propertyData } = await supabase
              .from("properties")
              .select("title")
              .eq("id", conv.property_id)
              .single();
            listingTitle = propertyData?.title;
          } else if (conv.car_id) {
            const { data: carData } = await supabase
              .from("cars")
              .select("title, make, model")
              .eq("id", conv.car_id)
              .single();
            listingTitle =
              carData?.title || `${carData?.make} ${carData?.model}`;
          } else if (conv.hotel_id) {
            const { data: hotelData } = await supabase
              .from("hotels")
              .select("title")
              .eq("id", conv.hotel_id)
              .single();
            listingTitle = hotelData?.title;
          }

          // Fetch last message
          const { data: lastMessageData } = await supabase
            .from("messages")
            .select("content, created_at, sender_id")
            .eq("conversation_id", conv.id)
            .order("created_at", { ascending: false })
            .limit(1)
            .single();

          // Count unread messages
          const { count: unreadCount } = await supabase
            .from("messages")
            .select("*", { count: "exact", head: true })
            .eq("conversation_id", conv.id)
            .neq("sender_id", user.id)
            .is("read_at", null);

          contactsList.push({
            id: conv.id.toString(),
            other_user_id: otherUserId,
            other_user_name: profileData
              ? `${profileData.first_name || ""} ${
                  profileData.last_name || ""
                }`.trim() || "Unknown User"
              : "Unknown User",
            other_user_avatar: profileData?.avatar_url || null,
            property_id: conv.property_id?.toString() || null,
            property_title: conv.property_id ? listingTitle : null,
            car_id: conv.car_id?.toString() || null,
            car_title: conv.car_id ? listingTitle : null,
            hotel_id: conv.hotel_id?.toString() || null,
            last_message_content: lastMessageData?.content || "No messages yet",
            last_message_at: lastMessageData?.created_at || conv.created_at,
            last_message_sender_id: lastMessageData?.sender_id || null,
            unread_count: unreadCount || 0,
            created_at: conv.created_at,
          });
        }
      }

      setContacts(contactsList);

      // Select the first contact if available
      if (contactsList.length > 0 && !selectedContact) {
        setSelectedContact(contactsList[0]);
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedContact) {
      loadConversation(selectedContact.id);
    }
  }, [selectedContact]);

  const loadConversation = async (conversationId: string) => {
    try {
      // Fetch messages for the selected conversation
      const { data: messages, error } = await supabase
        .from("messages")
        .select("id, sender_id, content, created_at, read_at")
        .eq("conversation_id", conversationId)
        .order("created_at", { ascending: true });

      if (error) throw error;

      // Transform messages to match our Message interface
      const transformedMessages: Message[] =
        messages?.map((msg) => ({
          id: msg.id.toString(),
          sender_id: msg.sender_id,
          content: msg.content,
          created_at: msg.created_at,
          read_at: msg.read_at,
        })) || [];

      setConversation(transformedMessages);

      // Mark messages as read if they're from the other participant
      const unreadMessages = messages?.filter(
        (msg) => msg.sender_id !== user?.id && !msg.read_at
      );

      if (unreadMessages && unreadMessages.length > 0) {
        await supabase
          .from("messages")
          .update({ read_at: new Date().toISOString() })
          .in(
            "id",
            unreadMessages.map((msg) => msg.id)
          );
      }
    } catch (error) {
      console.error("Error loading conversation:", error);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedContact || !user) return;

    setSendingMessage(true);

    try {
      // Send the message to the database
      const { data: messageData, error: messageError } = await supabase
        .from("messages")
        .insert({
          conversation_id: parseInt(selectedContact.id),
          sender_id: user.id,
          content: newMessage.trim(),
        })
        .select()
        .single();

      if (messageError) throw messageError;

      // Add the new message to the conversation
      const newMsg: Message = {
        id: messageData.id.toString(),
        sender_id: user.id,
        content: newMessage.trim(),
        created_at: messageData.created_at,
        read_at: null,
      };

      setConversation([...conversation, newMsg]);

      // Update the conversation's updated_at timestamp
      await supabase
        .from("conversations")
        .update({ updated_at: new Date().toISOString() })
        .eq("id", selectedContact.id);
      setNewMessage("");

      // Update the conversation's updated_at timestamp
      await supabase
        .from("conversations")
        .update({ updated_at: new Date().toISOString() })
        .eq("id", selectedContact.id);
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatTime = (date: Date) => {
    return format(date, "h:mm a");
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return format(date, "MMM d, yyyy");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Messages</h1>

      <div className="flex flex-col md:flex-row h-[calc(100vh-220px)] border rounded-lg overflow-hidden">
        {/* Contacts list */}
        <div className="w-full md:w-1/3 border-r">
          <div className="p-3 border-b">
            <Input placeholder="Search messages..." className="w-full" />
          </div>

          <ScrollArea className="h-[calc(100%-52px)]">
            {contacts.map((contact) => (
              <div
                key={contact.id}
                className={`flex items-center p-3 gap-3 cursor-pointer hover:bg-gray-100 ${
                  selectedContact?.id === contact.id ? "bg-gray-100" : ""
                }`}
                onClick={() => setSelectedContact(contact)}
              >
                <Avatar>
                  <AvatarImage src={contact.other_user_avatar || ""} />
                  <AvatarFallback>
                    {contact.other_user_name.charAt(0)}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium truncate">
                      {contact.other_user_name}
                    </h3>
                    <span className="text-xs text-gray-500">
                      {format(new Date(contact.last_message_at), "MMM d")}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">
                    {contact.last_message_content}
                  </p>
                  {(contact.property_title || contact.car_title) && (
                    <p className="text-xs text-gray-500 truncate">
                      Re: {contact.property_title || contact.car_title}
                    </p>
                  )}
                </div>

                {contact.unread_count > 0 && (
                  <span className="bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {contact.unread_count}
                  </span>
                )}
              </div>
            ))}
          </ScrollArea>
        </div>

        {/* Conversation */}
        <div className="flex-1 flex flex-col">
          {selectedContact ? (
            <>
              {/* Header */}
              <div className="p-3 border-b flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={selectedContact.other_user_avatar || ""} />
                  <AvatarFallback>
                    {selectedContact.other_user_name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="font-medium">
                    {selectedContact.other_user_name}
                  </h2>
                  {(selectedContact.property_title ||
                    selectedContact.car_title) && (
                    <p className="text-xs text-gray-500">
                      Re:{" "}
                      {selectedContact.property_title ||
                        selectedContact.car_title}
                    </p>
                  )}
                </div>
              </div>

              {/* Messages */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {conversation.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.sender_id === user?.id
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg p-3 ${
                          message.sender_id === user?.id
                            ? "bg-blue-500 text-white"
                            : "bg-gray-100"
                        }`}
                      >
                        <div className="text-sm">{message.content}</div>
                        <div
                          className={`text-xs mt-1 ${
                            message.sender_id === user?.id
                              ? "text-blue-50"
                              : "text-gray-500"
                          }`}
                        >
                          {format(new Date(message.created_at), "HH:mm")}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="p-3 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    disabled={!newMessage.trim() || sendingMessage}
                    onClick={handleSendMessage}
                  >
                    {sendingMessage ? "Sending..." : "Send"}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <p>Select a conversation to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>


    </div>
  );
};

export default HostMessages;
