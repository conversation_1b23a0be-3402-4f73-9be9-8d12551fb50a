import { Link } from "react-router-dom";
import { CarListing } from "@/hooks/useCarListings";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Car, MapPin, TestTube } from "lucide-react";
import { formatPrice } from "@/lib/price-utils";

interface CarCardProps {
  car: CarListing;
}

const CarCard = ({ car }: CarCardProps) => {
  return (
    <Link to={`/cars/${car.id}`}>
      <Card className="overflow-hidden hover:shadow-lg transition-shadow property-card-transition h-full flex flex-col">
        <div className="relative h-48">
          <img
            src={car.images?.[0] || "/placeholder.svg"}
            alt={car.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-2 right-2 flex gap-2">
            <Badge className="bg-accent text-white">
              {car.car_type.charAt(0).toUpperCase() + car.car_type.slice(1)}
            </Badge>
            {car.is_dummy && (
              <Badge className="bg-soft-orange text-white flex items-center gap-1 shadow-md">
                <TestTube className="w-3 h-3" />
                Display Only
              </Badge>
            )}
          </div>
        </div>

        <CardContent className="pt-4 flex-1 flex flex-col">
          <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
            <MapPin className="h-4 w-4" />
            <span>{car.location}</span>
          </div>

          <h3 className="text-lg font-semibold line-clamp-1">{car.title}</h3>

          <div className="flex items-center gap-4 mt-2 text-sm">
            <div className="flex items-center gap-1">
              <Car className="h-4 w-4" />
              <span>
                {car.make} {car.model}
              </span>
            </div>
            <div>
              <span>{car.year}</span>
            </div>
          </div>

          <div className="flex items-center gap-2 mt-2">
            <span>{car.seats} Seats</span>
            <span>•</span>
            <span>{car.transmission}</span>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between pt-0 border-t mt-auto">
          <div className="text-xl font-semibold">
            ${formatPrice(car.price_day)}
            <span className="text-sm font-normal">/day</span>
          </div>
          <div className="text-sm text-muted-foreground">
            From ${formatPrice(car.price_week)}/week
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
};

export default CarCard;
