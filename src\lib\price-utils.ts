/**
 * Utility functions for handling price formatting and precision
 */

/**
 * Formats a price value to ensure proper display without floating point issues
 * @param price - The price value (number or string)
 * @param decimals - Number of decimal places (default: 0 for whole numbers)
 * @returns Formatted price string
 */
export function formatPrice(
  price: number | string | null | undefined,
  decimals: number = 0
): string {
  if (price === null || price === undefined || price === "") {
    return "0";
  }

  const numPrice = typeof price === "string" ? parseFloat(price) : price;

  if (isNaN(numPrice)) {
    return "0";
  }

  // Round to avoid floating point precision issues
  const rounded =
    Math.round(numPrice * Math.pow(10, decimals)) / Math.pow(10, decimals);

  return rounded.toFixed(decimals);
}

/**
 * Parses a price input to ensure it's stored as a proper number
 * @param input - The input value (string or number)
 * @returns Properly parsed number
 */
export function parsePrice(input: string | number): number {
  if (typeof input === "number") {
    return input;
  }

  // For strings, handle empty or invalid input
  if (!input || input === "") {
    return 0;
  }

  // Remove any currency symbols and whitespace
  const cleanInput = input.toString().replace(/[$,\s]/g, "");

  const parsed = parseFloat(cleanInput);
  if (isNaN(parsed)) {
    return 0;
  }

  return parsed;
}

/**
 * Formats a price for display with currency symbol
 * @param price - The price value
 * @param currency - Currency symbol (default: '$')
 * @param decimals - Number of decimal places (default: 0)
 * @returns Formatted price with currency
 */
export function formatCurrency(
  price: number | string | null | undefined,
  currency: string = "$",
  decimals: number = 0
): string {
  return `${currency}${formatPrice(price, decimals)}`;
}

/**
 * Validates that a price is a valid positive number
 * @param price - The price to validate
 * @returns True if valid, false otherwise
 */
export function isValidPrice(price: number | string): boolean {
  const numPrice = typeof price === "string" ? parseFloat(price) : price;
  return !isNaN(numPrice) && numPrice > 0;
}
