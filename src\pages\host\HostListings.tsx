import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Database } from "@/integrations/supabase/types";
import { Eye, EyeOff, Pencil, Trash2 } from "lucide-react";
import {
  deletePropertyImages,
  deleteCarImages,
  deleteHotelImages,
} from "@/lib/storage-utils";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

type ListingStatus = Database["public"]["Enums"]["listing_status"];

const HostListings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(true);
  const [properties, setProperties] = useState<any[]>([]);
  const [hotels, setHotels] = useState<any[]>([]);
  const [cars, setCars] = useState<any[]>([]);

  useEffect(() => {
    if (user) {
      fetchUserListings();
    }
  }, [user]);

  const fetchUserListings = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Fetch properties
      const { data: propertiesData, error: propertiesError } = await supabase
        .from("properties")
        .select("*")
        .eq("owner_id", user.id);

      if (propertiesError) throw propertiesError;
      setProperties(propertiesData || []);

      // Fetch hotels with room types for pricing calculation
      const { data: hotelsData, error: hotelsError } = await supabase
        .from("hotels")
        .select(
          `
          *,
          room_types (*)
        `
        )
        .eq("owner_id", user.id);

      if (hotelsError) throw hotelsError;

      // Calculate min_price for each hotel
      const hotelsWithPricing = (hotelsData || []).map((hotel) => {
        const min_price =
          hotel.room_types && hotel.room_types.length > 0
            ? Math.min(...hotel.room_types.map((rt: any) => rt.base_price))
            : 0;

        return {
          ...hotel,
          min_price,
        };
      });

      setHotels(hotelsWithPricing);

      // Fetch cars
      const { data: carsData, error: carsError } = await supabase
        .from("cars")
        .select("*")
        .eq("owner_id", user.id);

      if (carsError) throw carsError;
      setCars(carsData || []);
    } catch (error: any) {
      console.error("Error fetching listings:", error);
      toast.error("Failed to fetch your listings");
    } finally {
      setLoading(false);
    }
  };

  const updateListingStatus = async (
    id: string,
    table: "properties" | "hotels" | "cars",
    status: ListingStatus
  ) => {
    try {
      const { error } = await supabase
        .from(table)
        .update({ status })
        .eq("id", id);

      if (error) throw error;

      toast.success(
        `Listing ${
          status === "approved" ? "made visible" : "hidden from public"
        } successfully`
      );

      // Refresh host listings
      fetchUserListings();

      // Invalidate public listing caches to reflect changes immediately
      queryClient.invalidateQueries({ queryKey: ["properties"] });
      queryClient.invalidateQueries({ queryKey: ["hotels"] });
      queryClient.invalidateQueries({ queryKey: ["cars"] });
      queryClient.invalidateQueries({ queryKey: ["featured-properties"] });
    } catch (error: any) {
      console.error("Error updating listing status:", error);
      toast.error("Failed to update listing status");
    }
  };

  const toggleListingActivation = async (
    id: string,
    table: "properties" | "hotels" | "cars",
    currentActiveStatus: boolean
  ) => {
    try {
      const { error } = await supabase
        .from(table)
        .update({ is_active: !currentActiveStatus })
        .eq("id", id);

      if (error) throw error;

      toast.success(
        `Listing ${
          !currentActiveStatus
            ? "is now visible to public"
            : "is now hidden from public"
        } successfully`
      );

      // Refresh host listings
      fetchUserListings();

      // Invalidate public listing caches to reflect changes immediately
      queryClient.invalidateQueries({ queryKey: ["properties"] });
      queryClient.invalidateQueries({ queryKey: ["hotels"] });
      queryClient.invalidateQueries({ queryKey: ["cars"] });
      queryClient.invalidateQueries({ queryKey: ["featured-properties"] });
    } catch (error: any) {
      console.error("Error updating listing activation:", error);
      toast.error("Failed to update listing activation");
    }
  };

  const deleteListing = async (
    id: string,
    table: "properties" | "hotels" | "cars"
  ) => {
    try {
      // First, get the listing data to access images
      const { data: listingData, error: fetchError } = await supabase
        .from(table)
        .select("images")
        .eq("id", id)
        .single();

      if (fetchError) throw fetchError;

      // Delete the listing from database
      const { error: deleteError } = await supabase
        .from(table)
        .delete()
        .eq("id", id);

      if (deleteError) throw deleteError;

      // Delete associated images from storage
      if (listingData?.images && listingData.images.length > 0) {
        let deleteImageFunction;
        switch (table) {
          case "properties":
            deleteImageFunction = deletePropertyImages;
            break;
          case "cars":
            deleteImageFunction = deleteCarImages;
            break;
          case "hotels":
            deleteImageFunction = deleteHotelImages;
            break;
        }

        const { failed } = await deleteImageFunction(listingData.images);
        if (failed.length > 0) {
          console.warn(`Failed to delete ${failed.length} images from storage`);
        }
      }

      toast.success("Listing deleted successfully");

      // Refresh host listings
      fetchUserListings();

      // Invalidate public listing caches to reflect changes immediately
      queryClient.invalidateQueries({ queryKey: ["properties"] });
      queryClient.invalidateQueries({ queryKey: ["hotels"] });
      queryClient.invalidateQueries({ queryKey: ["cars"] });
      queryClient.invalidateQueries({ queryKey: ["featured-properties"] });
    } catch (error: any) {
      console.error("Error deleting listing:", error);
      toast.error("Failed to delete listing");
    }
  };

  const renderListingCard = (
    listing: any,
    type: "properties" | "hotels" | "cars"
  ) => {
    const isProperty = type === "properties";
    const isHotel = type === "hotels";
    const isCar = type === "cars";
    const isApproved = listing.status === "approved";
    const isActive = listing.is_active !== false; // Default to true if field doesn't exist yet

    return (
      <Card key={listing.id} className="overflow-hidden">
        <div className="aspect-video relative">
          <img
            src={listing.images?.[0] || "/placeholder.svg"}
            alt={listing.title || `${listing.make} ${listing.model}`}
            className="object-cover w-full h-full"
          />
          <Badge
            variant={isActive ? "default" : "secondary"}
            className="absolute top-2 right-2"
          >
            {listing.status}
          </Badge>
        </div>
        <CardContent className="p-4">
          <h3 className="font-bold truncate">{listing.title || "No Title"}</h3>
          {isCar && (
            <p className="text-sm text-muted-foreground">
              {listing.make} {listing.model} ({listing.year})
            </p>
          )}
          <p className="text-sm text-muted-foreground truncate">
            {listing.location || "No Location"}
          </p>
          <div className="mt-2">
            <span className="font-semibold">
              {isProperty
                ? `$${listing.price}/night`
                : isCar
                ? `$${listing.price_day}/day`
                : isHotel && listing.min_price && listing.min_price > 0
                ? `From $${listing.min_price}/night`
                : "Contact for rates"}
            </span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between p-4 pt-0">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const editPath = isProperty
                  ? `/listings/edit/${listing.id}`
                  : isHotel
                  ? `/hotels/edit/${listing.id}`
                  : `/cars/edit/${listing.id}`;
                navigate(editPath);
              }}
            >
              Edit
            </Button>
            {/* Only show Hide/Show button for approved listings */}
            {isApproved && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const table = isProperty
                        ? "properties"
                        : isHotel
                        ? "hotels"
                        : "cars";
                      toggleListingActivation(listing.id, table, isActive);
                    }}
                  >
                    {isActive ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {isActive
                      ? "Hide listing from public"
                      : "Show listing to public"}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm">
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Listing</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this listing? This action
                  cannot be undone and will permanently remove your listing from
                  the platform.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    const table = isProperty
                      ? "properties"
                      : isHotel
                      ? "hotels"
                      : "cars";
                    deleteListing(listing.id, table);
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  Delete Listing
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </CardFooter>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Your Listings</h1>
        <div className="flex space-x-2">
          <Button
            onClick={() => {
              sessionStorage.setItem("createListingFrom", "host");
              navigate("/listings/create?from=host");
            }}
          >
            Add Property
          </Button>
          <Button
            onClick={() => {
              sessionStorage.setItem("createHotelFrom", "host");
              navigate("/hotels/create?from=host");
            }}
            variant="outline"
          >
            Add Hotel
          </Button>
          <Button
            onClick={() => {
              sessionStorage.setItem("createCarListingFrom", "host");
              navigate("/cars/create?from=host");
            }}
            variant="outline"
          >
            Add Car
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Listings</TabsTrigger>
          <TabsTrigger value="properties">Properties</TabsTrigger>
          <TabsTrigger value="hotels">Hotels</TabsTrigger>
          <TabsTrigger value="cars">Cars</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-4">
          {properties.length === 0 &&
          hotels.length === 0 &&
          cars.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">
                You don't have any listings yet.
              </p>
              <div className="flex justify-center space-x-2">
                <Button
                  onClick={() => {
                    sessionStorage.setItem("createListingFrom", "host");
                    navigate("/listings/create?from=host");
                  }}
                >
                  Add Property
                </Button>
                <Button
                  onClick={() => {
                    sessionStorage.setItem("createHotelFrom", "host");
                    navigate("/hotels/create?from=host");
                  }}
                  variant="outline"
                >
                  Add Hotel
                </Button>
                <Button
                  onClick={() => {
                    sessionStorage.setItem("createCarListingFrom", "host");
                    navigate("/cars/create?from=host");
                  }}
                  variant="outline"
                >
                  Add Car
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {properties.map((property) =>
                renderListingCard(property, "properties")
              )}
              {hotels.map((hotel) => renderListingCard(hotel, "hotels"))}
              {cars.map((car) => renderListingCard(car, "cars"))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="properties" className="mt-4">
          {properties.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">
                You don't have any property listings yet.
              </p>
              <Button onClick={() => navigate("/listings/create")}>
                Add Property
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {properties.map((property) =>
                renderListingCard(property, "properties")
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="hotels" className="mt-4">
          {hotels.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">
                You don't have any hotel listings yet.
              </p>
              <Button
                onClick={() => {
                  sessionStorage.setItem("createHotelFrom", "host");
                  navigate("/hotels/create?from=host");
                }}
                variant="outline"
              >
                Add Hotel
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {hotels.map((hotel) => renderListingCard(hotel, "hotels"))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="cars" className="mt-4">
          {cars.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-muted-foreground mb-4">
                You don't have any car listings yet.
              </p>
              <Button
                onClick={() => navigate("/cars/create")}
                variant="outline"
              >
                Add Car
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {cars.map((car) => renderListingCard(car, "cars"))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HostListings;
