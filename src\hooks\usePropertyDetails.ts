import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export const usePropertyDetails = (id: string | undefined) => {
  return useQuery({
    queryKey: ["property", id],
    queryFn: async () => {
      if (!id) throw new Error("Property ID is required");

      // First get the property details
      const { data: propertyData, error: propertyError } = await supabase
        .from("properties")
        .select("*")
        .eq("id", id)
        .single();

      if (propertyError) throw propertyError;

      // Check if property is approved and active for public access
      if (
        propertyData.status !== "approved" ||
        propertyData.is_active === false
      ) {
        throw new Error("This property is not available for viewing");
      }

      // Then get the owner profile separately (with better error handling)
      let profileData = null;
      try {
        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("id, first_name, last_name, avatar_url")
          .eq("id", propertyData.owner_id)
          .single();

        if (!profileError) {
          profileData = profile;
        }
      } catch (profileError) {
        // Continue without profile data
      }

      const data = {
        ...propertyData,
        profiles: profileData,
      };

      // Fetch reviews for this property
      const { data: reviewsData, error: reviewsError } = await supabase
        .from("reviews")
        .select("rating")
        .eq("property_id", id);

      if (reviewsError) throw reviewsError;

      const reviews = reviewsData || [];
      const reviewCount = reviews.length;
      const avgRating =
        reviewCount > 0
          ? reviews.reduce((sum, r) => sum + (r.rating || 0), 0) / reviewCount
          : 0;

      return {
        id: data.id,
        title: data.title || "Untitled Property",
        location: data.location || "Location not specified",
        latitude: data.latitude,
        longitude: data.longitude,
        formatted_address: data.formatted_address,
        price: Number(data.price) || 0,
        rating: avgRating,
        reviews: reviewCount,
        images: data.images || [],
        beds: Number(data.beds) || 0,
        baths: Number(data.baths) || 0,
        description: data.description || "No description available",
        features: data.features || [],
        isSuperHost: false,
        is_dummy: data.is_dummy || false,
        owner: data.profiles
          ? {
              id: data.profiles.id,
              name:
                `${data.profiles.first_name || ""} ${
                  data.profiles.last_name || ""
                }`.trim() || "Unknown Host",
              avatar_url: data.profiles.avatar_url,
            }
          : null,
      };
    },
    enabled: !!id,
  });
};
