# Property Management Test Cases

## Test Category: Property Management
**Priority**: Critical
**Estimated Time**: 60 minutes

---

## TC-PROP-001: Create New Property Listing
**Objective**: Verify hosts can create new property listings

### Test Steps:
1. <PERSON><PERSON> as property host
2. Navigate to "Create Listing" page
3. Fill in property details:
   - Title: "Beautiful Beachfront Villa"
   - Description: "Stunning 3-bedroom villa with ocean views"
   - Location: "Kololi, The Gambia"
   - Price: "150" (per night)
   - Bedrooms: "3"
   - Bathrooms: "2"
4. Select property features (WiFi, Pool, etc.)
5. Upload property images (minimum 3)
6. Set location on map
7. Click "Create Listing" button
8. Verify success message and redirect

### Expected Results:
- Form accepts all valid inputs
- Image upload works properly
- Map location selection functions
- Property is created with "pending" status
- Host is redirected to listings page
- New property appears in host's listings

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-002: Edit Existing Property
**Objective**: Verify hosts can edit their property listings

### Test Steps:
1. <PERSON><PERSON> as property host
2. Navigate to host dashboard/listings
3. Select existing property to edit
4. Click "Edit" button
5. Modify property details:
   - Update title
   - Change price
   - Add/remove features
   - Upload additional images
6. Update location if needed
7. Save changes
8. Verify updates are reflected

### Expected Results:
- Edit form loads with existing data
- All fields are editable
- Image management works (add/remove)
- Changes are saved successfully
- Updated information displays correctly
- Property maintains its approval status

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-003: Property Image Management
**Objective**: Verify image upload and management functionality

### Test Steps:
1. Create or edit a property listing
2. Test image upload:
   - Upload valid image formats (JPG, PNG)
   - Upload multiple images at once
   - Try uploading invalid formats
   - Test large file sizes
3. Verify image preview functionality
4. Test image reordering (if available)
5. Test image deletion
6. Save listing and verify images persist

### Expected Results:
- Valid images upload successfully
- Invalid formats are rejected with error
- Large files are handled appropriately
- Image previews display correctly
- Image management functions work
- Images are saved with the listing

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-004: Property Features Selection
**Objective**: Verify property features/amenities selection

### Test Steps:
1. Create or edit property listing
2. Test feature selection:
   - Select multiple features
   - Deselect features
   - Verify all available features display
3. Common features to test:
   - WiFi
   - Pool
   - Air Conditioning
   - Kitchen
   - Parking
   - Pet Friendly
4. Save listing and verify features persist

### Expected Results:
- All features are selectable
- Multiple selections work
- Deselection works properly
- Features are saved with listing
- Features display on property detail page

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-005: Location and Map Integration
**Objective**: Verify location setting and map functionality

### Test Steps:
1. Create or edit property listing
2. Test location input:
   - Type location in text field
   - Verify autocomplete suggestions
   - Select location from suggestions
3. Test map interaction:
   - Verify map loads correctly
   - Click on map to set location
   - Drag marker to adjust location
4. Verify coordinates are captured
5. Save listing and verify location data

### Expected Results:
- Location autocomplete works
- Map loads and displays correctly
- Location can be set via text or map
- Coordinates are captured accurately
- Location data is saved properly
- Property shows correct location on listings

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-006: Property Validation Rules
**Objective**: Verify form validation for property creation

### Test Steps:
1. Navigate to create property page
2. Test required field validation:
   - Submit form with empty title
   - Submit with empty description
   - Submit with empty location
   - Submit with zero price
3. Test field format validation:
   - Enter negative price
   - Enter non-numeric values in number fields
   - Enter extremely long text in fields
4. Test image requirements:
   - Submit without images
   - Upload too many images (if limit exists)

### Expected Results:
- Required fields show validation errors
- Invalid formats are rejected
- Appropriate error messages display
- Form cannot be submitted with invalid data
- Validation messages are clear and helpful

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-007: Property Status Management
**Objective**: Verify property status handling (pending/approved/rejected)

### Test Steps:
1. Create new property listing
2. Verify initial status is "pending"
3. Check property visibility:
   - Property should not appear in public listings
   - Property should appear in host's dashboard
4. Simulate admin approval (if possible)
5. Verify approved property appears in public listings
6. Test property deactivation/reactivation

### Expected Results:
- New properties start as "pending"
- Pending properties are not publicly visible
- Approved properties appear in public listings
- Status changes are reflected immediately
- Host can see all their properties regardless of status

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-008: Property Deletion
**Objective**: Verify hosts can delete their properties

### Test Steps:
1. Login as property host
2. Navigate to property listings
3. Select property to delete
4. Click delete/remove button
5. Verify confirmation dialog appears
6. Confirm deletion
7. Verify property is removed from listings
8. Check if property has active bookings
9. Test deletion prevention for properties with bookings

### Expected Results:
- Delete option is available for host's properties
- Confirmation dialog prevents accidental deletion
- Property is removed after confirmation
- Properties with active bookings cannot be deleted
- Appropriate error message for protected properties

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-009: Property Listing View
**Objective**: Verify property details display correctly

### Test Steps:
1. Create and approve a property listing
2. Navigate to public property listings
3. Find and click on the test property
4. Verify property detail page displays:
   - All property information
   - Image gallery
   - Features/amenities
   - Location map
   - Pricing information
   - Host information
   - Booking widget
5. Test image gallery functionality
6. Test map interaction

### Expected Results:
- All property data displays correctly
- Images load and gallery functions work
- Map shows correct location
- Booking widget is functional
- Host contact information is available
- Page is responsive on different devices

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PROP-010: Property Search and Filtering
**Objective**: Verify properties can be found through search

### Test Steps:
1. Create multiple test properties with different:
   - Locations
   - Price ranges
   - Features
   - Property types
2. Navigate to property listings page
3. Test search functionality:
   - Search by location
   - Search by property name
   - Use filters (price, features, etc.)
4. Verify search results are accurate
5. Test sorting options (price, date, rating)

### Expected Results:
- Search returns relevant results
- Filters work correctly
- Sorting functions properly
- No results message displays when appropriate
- Search is case-insensitive
- Results update in real-time

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
