# Messaging System Test Cases

## Test Category: Messaging System
**Priority**: High
**Estimated Time**: 40 minutes

---

## TC-MSG-001: Contact Host from Property Page
**Objective**: Verify users can contact hosts from property listings

### Test Steps:
1. Navigate to a property detail page
2. Locate "Contact Host" button
3. Click "Contact Host" button
4. Verify message modal/form opens
5. Enter message: "Hi, I'm interested in your property. Is it available for next weekend?"
6. Click "Send Message" button
7. Verify success confirmation
8. Check if conversation is created
9. Verify host receives notification

### Expected Results:
- Contact host button is visible and functional
- Message form opens correctly
- Message can be typed and sent
- Success confirmation appears
- Conversation is created in messaging system
- Host receives message notification
- Message appears in both user's and host's message list

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-002: Real-time Messaging
**Objective**: Verify real-time messaging functionality

### Test Steps:
1. <PERSON><PERSON> as User A and navigate to messages
2. Open browser in incognito mode and login as User B
3. Start conversation from User A to User B
4. Send message from User A: "Hello, how are you?"
5. Check if message appears immediately in User B's interface
6. Reply from User B: "Hi! I'm doing well, thanks!"
7. Verify message appears in User A's interface
8. Test multiple rapid messages
9. Test message delivery status

### Expected Results:
- Messages appear in real-time without page refresh
- Message delivery is immediate
- Both users see the conversation simultaneously
- Message order is maintained correctly
- Delivery status is shown (sent, delivered, read)
- No message duplication occurs

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-003: Message Read Status
**Objective**: Verify message read status tracking

### Test Steps:
1. Send message from User A to User B
2. Verify message shows as "sent" for User A
3. Login as User B and view messages
4. Open the conversation with User A
5. Verify message shows as "delivered" for User A
6. Read the message as User B
7. Verify message shows as "read" for User A
8. Test with multiple messages
9. Test read status with offline users

### Expected Results:
- Messages show correct status (sent/delivered/read)
- Status updates in real-time
- Read status changes when message is viewed
- Multiple messages track status independently
- Offline users don't affect read status
- Status indicators are clear and visible

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-004: Conversation Management
**Objective**: Verify conversation creation and management

### Test Steps:
1. Test conversation creation:
   - From property page
   - From car listing page
   - From hotel page
   - Direct user-to-user messaging
2. Verify conversation list displays:
   - All active conversations
   - Last message preview
   - Timestamp of last message
   - Unread message indicators
3. Test conversation sorting:
   - Most recent conversations first
   - Unread conversations prioritized
4. Test conversation search/filtering

### Expected Results:
- Conversations are created for different listing types
- Conversation list shows all active chats
- Last message and timestamp are accurate
- Unread indicators work correctly
- Conversations are sorted by recency
- Search/filter functionality works
- Conversation context (property/car/hotel) is maintained

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-005: Message History and Persistence
**Objective**: Verify message history is maintained correctly

### Test Steps:
1. Create conversation and send multiple messages
2. Close browser and reopen
3. Navigate to messages and verify:
   - All messages are still visible
   - Message order is correct
   - Timestamps are accurate
4. Test with long conversation history
5. Test message pagination (if implemented)
6. Verify messages persist across sessions
7. Test message history for different listing types

### Expected Results:
- All messages are preserved
- Message order is chronological
- Timestamps are accurate and formatted correctly
- Long conversations load properly
- Pagination works if implemented
- Messages persist across browser sessions
- Context (property/car/hotel) is maintained in history

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-006: Message Notifications
**Objective**: Verify message notification system

### Test Steps:
1. Send message from User A to User B
2. Verify User B receives notification:
   - In-app notification (if implemented)
   - Email notification (if enabled)
   - Browser notification (if enabled)
3. Test notification content:
   - Sender name
   - Message preview
   - Property/listing context
4. Test notification settings:
   - Enable/disable notifications
   - Notification preferences
5. Test notification for offline users

### Expected Results:
- Notifications are sent when messages are received
- Notification content is informative
- Context (property/car/hotel) is included
- Notification settings can be managed
- Offline users receive notifications when they return
- Notifications don't spam users
- Notification links work correctly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-007: Message Input and Formatting
**Objective**: Verify message input functionality

### Test Steps:
1. Test message input field:
   - Type short messages
   - Type long messages
   - Test character limits (if any)
2. Test special characters:
   - Emojis
   - Special symbols
   - Multiple languages
3. Test message formatting:
   - Line breaks
   - Multiple paragraphs
   - URLs (auto-linking if implemented)
4. Test input validation:
   - Empty messages
   - Whitespace-only messages
   - Very long messages

### Expected Results:
- Message input accepts all valid characters
- Character limits are enforced appropriately
- Special characters display correctly
- Line breaks are preserved
- URLs are handled properly
- Empty/invalid messages are rejected
- Input field is responsive and user-friendly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-008: Host Message Management
**Objective**: Verify host-specific messaging features

### Test Steps:
1. Login as host and navigate to messages
2. Verify host can see:
   - Messages from all properties
   - Messages from car listings
   - Messages from hotel bookings
3. Test host response capabilities:
   - Reply to guest inquiries
   - Initiate conversations with guests
   - Manage multiple conversations
4. Test host message organization:
   - Filter by property/car/hotel
   - Sort by urgency/date
   - Mark conversations as resolved

### Expected Results:
- Host sees all relevant conversations
- Messages are organized by listing type
- Host can respond efficiently
- Multiple conversations are manageable
- Filtering and sorting work correctly
- Host can track conversation status
- Host interface is optimized for business use

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-009: Message Security and Privacy
**Objective**: Verify message security measures

### Test Steps:
1. Test message privacy:
   - Users can only see their own conversations
   - Messages are not visible to unauthorized users
2. Test message content security:
   - HTML/script injection prevention
   - Malicious link protection
   - Spam prevention measures
3. Test conversation access control:
   - Only participants can access conversation
   - Admin access controls (if applicable)
4. Test message deletion/archiving:
   - Users can delete their messages
   - Conversation privacy is maintained

### Expected Results:
- Messages are private to participants
- Unauthorized access is prevented
- Malicious content is filtered
- Spam protection is active
- Access controls work correctly
- Message deletion works properly
- Privacy is maintained throughout

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-MSG-010: Mobile Messaging Experience
**Objective**: Verify messaging works well on mobile devices

### Test Steps:
1. Test messaging on mobile browser:
   - Message list view
   - Conversation view
   - Message input
2. Test mobile-specific features:
   - Touch interactions
   - Keyboard behavior
   - Screen orientation changes
3. Test mobile notifications:
   - Push notifications (if implemented)
   - Badge counts
   - Notification sounds
4. Test mobile performance:
   - Loading speed
   - Scrolling performance
   - Battery usage

### Expected Results:
- Mobile interface is responsive
- Touch interactions work smoothly
- Keyboard doesn't obstruct interface
- Orientation changes are handled
- Mobile notifications work
- Performance is acceptable
- Battery usage is reasonable

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
