import React, { useState, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Upload, Star, StarOff, Image as ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageFile {
  file: File;
  preview: string;
  id: string;
}

interface ExistingImage {
  url: string;
  id: string;
  isThumbnail?: boolean;
}

interface ImageUploaderProps {
  onImagesChange: (files: File[]) => void;
  onThumbnailChange?: (thumbnailIndex: number) => void;
  onExistingImagesChange?: (images: ExistingImage[]) => void;
  existingImages?: ExistingImage[];
  maxImages?: number;
  acceptedFileTypes?: string[];
  maxFileSize?: number; // in MB
  className?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImagesChange,
  onThumbnailChange,
  onExistingImagesChange,
  existingImages = [],
  maxImages = 10,
  acceptedFileTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"],
  maxFileSize = 5,
  className,
}) => {
  const [newImages, setNewImages] = useState<ImageFile[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const totalImages = existingImages.length + newImages.length;

  const createImageFile = (file: File): ImageFile => ({
    file,
    preview: URL.createObjectURL(file),
    id: Math.random().toString(36).substr(2, 9),
  });

  const validateFile = (file: File): string | null => {
    if (!acceptedFileTypes.includes(file.type)) {
      return `File type ${
        file.type
      } is not supported. Please use: ${acceptedFileTypes.join(", ")}`;
    }
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }
    return null;
  };

  const handleFiles = useCallback(
    (files: FileList) => {
      const validFiles: File[] = [];
      const errors: string[] = [];

      Array.from(files).forEach((file) => {
        if (totalImages + validFiles.length >= maxImages) {
          errors.push(`Maximum ${maxImages} images allowed`);
          return;
        }

        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          validFiles.push(file);
        }
      });

      if (errors.length > 0) {
        // You can replace this with your toast notification
        console.error("Upload errors:", errors);
      }

      if (validFiles.length > 0) {
        const imageFiles = validFiles.map(createImageFile);
        const updatedImages = [...newImages, ...imageFiles];
        setNewImages(updatedImages);
        onImagesChange(updatedImages.map((img) => img.file));
      }
    },
    [newImages, totalImages, maxImages, onImagesChange]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFiles(files);
      }
    },
    [handleFiles]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files) {
        handleFiles(files);
      }
      // Reset input value to allow selecting the same file again
      e.target.value = "";
    },
    [handleFiles]
  );

  const removeNewImage = (id: string) => {
    const updatedImages = newImages.filter((img) => img.id !== id);
    setNewImages(updatedImages);
    onImagesChange(updatedImages.map((img) => img.file));

    // Clean up object URL
    const imageToRemove = newImages.find((img) => img.id === id);
    if (imageToRemove) {
      URL.revokeObjectURL(imageToRemove.preview);
    }
  };

  const removeExistingImage = (id: string) => {
    if (onExistingImagesChange) {
      const updatedImages = existingImages.filter((img) => img.id !== id);
      onExistingImagesChange(updatedImages);
    }
  };

  const setThumbnail = (index: number, isExisting: boolean = false) => {
    if (isExisting) {
      // Update existing images to mark thumbnail
      if (onExistingImagesChange) {
        const updatedImages = existingImages.map((img, i) => ({
          ...img,
          isThumbnail: i === index,
        }));
        onExistingImagesChange(updatedImages);
      }
    } else {
      // For new images, just track the index
      const adjustedIndex = existingImages.length + index;
      setThumbnailIndex(adjustedIndex);
      if (onThumbnailChange) {
        onThumbnailChange(adjustedIndex);
      }
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <Card
        className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragOver
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/25",
          totalImages >= maxImages && "opacity-50 cursor-not-allowed"
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={totalImages < maxImages ? openFileDialog : undefined}
      >
        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
          <Upload className="h-10 w-10 text-muted-foreground mb-2" />
          <p className="text-sm font-medium mb-1">
            {totalImages >= maxImages
              ? `Maximum ${maxImages} images reached`
              : "Click to upload or drag and drop"}
          </p>
          <p className="text-xs text-muted-foreground">
            PNG, JPG, WEBP up to {maxFileSize}MB ({maxImages - totalImages}{" "}
            remaining)
          </p>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedFileTypes.join(",")}
        onChange={handleFileInput}
        className="hidden"
      />

      {/* Image Previews */}
      {(existingImages.length > 0 || newImages.length > 0) && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">
              Images ({totalImages}/{maxImages})
            </h4>
            <p className="text-xs text-muted-foreground">
              Click the star to set as thumbnail
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {/* Existing Images */}
            {existingImages.map((image, index) => (
              <div key={`existing-${image.id}`} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                  <img
                    src={image.url}
                    alt={`Existing image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Thumbnail Badge */}
                {image.isThumbnail && (
                  <Badge className="absolute top-2 left-2 bg-yellow-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Thumbnail
                  </Badge>
                )}

                {/* Controls */}
                <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    type="button"
                    size="sm"
                    variant="secondary"
                    className="h-6 w-6 p-0"
                    onClick={() => setThumbnail(index, true)}
                    title="Set as thumbnail"
                  >
                    {image.isThumbnail ? (
                      <Star className="h-3 w-3 fill-current" />
                    ) : (
                      <StarOff className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    variant="destructive"
                    className="h-6 w-6 p-0"
                    onClick={() => removeExistingImage(image.id)}
                    title="Remove image"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}

            {/* New Images */}
            {newImages.map((image, index) => {
              const globalIndex = existingImages.length + index;
              const isThumbnail = globalIndex === thumbnailIndex;

              return (
                <div key={image.id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                    <img
                      src={image.preview}
                      alt={`New image ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Thumbnail Badge */}
                  {isThumbnail && (
                    <Badge className="absolute top-2 left-2 bg-yellow-500 text-white">
                      <Star className="h-3 w-3 mr-1" />
                      Thumbnail
                    </Badge>
                  )}

                  {/* Controls */}
                  <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      type="button"
                      size="sm"
                      variant="secondary"
                      className="h-6 w-6 p-0"
                      onClick={() => setThumbnail(index, false)}
                      title="Set as thumbnail"
                    >
                      {isThumbnail ? (
                        <Star className="h-3 w-3 fill-current" />
                      ) : (
                        <StarOff className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      type="button"
                      size="sm"
                      variant="destructive"
                      className="h-6 w-6 p-0"
                      onClick={() => removeNewImage(image.id)}
                      title="Remove image"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
