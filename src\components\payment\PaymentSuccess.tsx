import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/layout/Navbar";

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [bookingDetails, setBookingDetails] = useState<any>(null);
  const [verificationCalled, setVerificationCalled] = useState(false);

  const sessionId = searchParams.get("session_id");
  const bookingId = searchParams.get("booking_id");
  const isCarBooking = searchParams.get("car_booking") === "true";


  useEffect(() => {
    const verifyPayment = async () => {
      // Create a unique key for this payment session
      const verificationKey = `payment_verified_${sessionId}_${bookingId}`;

      // Check if verification was already done for this session
      const alreadyVerified = localStorage.getItem(verificationKey);
      if (alreadyVerified) {
        setLoading(false);
        return;
      }

      // Prevent duplicate calls during this session
      if (verificationCalled) {
        return;
      }

      if (!sessionId || !bookingId) {
        toast({
          title: "Missing information",
          description: "Could not verify payment due to missing information.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setVerificationCalled(true); // Mark as called to prevent duplicates
        // Verify the payment status
        const { data, error } = await supabase.functions.invoke(
          "verify-stripe-payment",
          {
            body: isCarBooking
              ? { sessionId, carBookingId: bookingId }
              : { sessionId, bookingId },
          }
        );

        if (error) throw error;


        if (data.paymentStatus === "completed") {
          // Fetch booking details based on booking type
          let bookingData, bookingError;

          if (isCarBooking) {
            // First get the car booking with cars data
            const { data: carBooking, error: carBookingError } = await supabase
              .from("car_bookings")
              .select(
                `
                *,
                cars (
                  id,
                  make,
                  model,
                  location,
                  owner_id
                )
              `
              )
              .eq("id", bookingId)
              .single();

            if (carBookingError) {
              console.error("Car booking error:", carBookingError);
              bookingError = carBookingError;
              bookingData = null;
            } else {
              // Then get the user profile separately
              const { data: profile, error: profileError } = await supabase
                .from("profiles")
                .select("id, first_name, last_name, email")
                .eq("id", carBooking.user_id)
                .single();

              if (profileError) {
                console.warn("Could not fetch user profile:", profileError);
              }

              bookingData = {
                ...carBooking,
                profiles: profile,
              };
              bookingError = null;
            }
          } else {
            // First get the booking with properties data
            const { data: booking, error: bookingErr } = await supabase
              .from("bookings")
              .select(
                `
                *,
                properties(*)
              `
              )
              .eq("id", bookingId)
              .single();

            if (bookingErr) {
              console.error("Booking error:", bookingErr);
              bookingData = null;
              bookingError = bookingErr;
            } else {
              // Then get the user profile separately
              const { data: profile, error: profileError } = await supabase
                .from("profiles")
                .select("id, first_name, last_name, email")
                .eq("id", booking.user_id)
                .single();

              if (profileError) {
                console.warn("Could not fetch user profile:", profileError);
              }

              bookingData = {
                ...booking,
                profiles: profile,
              };
              bookingError = null;
            }
          }

          if (bookingError) throw bookingError;
          setBookingDetails(bookingData);

          // Mark verification as completed in localStorage to prevent future duplicates
          const verificationKey = `payment_verified_${sessionId}_${bookingId}`;
          localStorage.setItem(verificationKey, "true");

          // Note: Confirmation emails are automatically sent by the verify-stripe-payment function
          // No need to send them again here to avoid duplicates

          toast({
            title: "Payment successful!",
            description:
              "Your booking has been confirmed and emails have been sent.",
          });
        } else {
          toast({
            title: "Payment processing",
            description:
              "Your payment is being processed. We'll notify you when it's complete.",
          });
        }
      } catch (error: any) {
        console.error("Payment verification error:", error);
        toast({
          title: "Verification failed",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId, bookingId, toast]);

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-3xl mx-auto bg-card rounded-lg shadow-md p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-muted-teal/20 rounded-full mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-muted-teal"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              Payment Successful!
            </h1>
            <p className="text-muted-foreground mt-2">
              Your booking has been confirmed.
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center my-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
            </div>
          ) : bookingDetails ? (
            <div className="space-y-6">
              <div className="border-b pb-4">
                <h2 className="text-xl font-semibold mb-2">Booking Details</h2>
                <div className="space-y-2">
                  {isCarBooking ? (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Car:</span>
                        <span className="font-medium">
                          {bookingDetails.cars?.make}{" "}
                          {bookingDetails.cars?.model}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span>{bookingDetails.cars?.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Start Date:</span>
                        <span>
                          {new Date(
                            bookingDetails.start_date
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">End Date:</span>
                        <span>
                          {new Date(
                            bookingDetails.end_date
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration Type:</span>
                        <span className="capitalize">
                          {bookingDetails.duration_type}
                        </span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Property:</span>
                        <span className="font-medium">
                          {bookingDetails.properties?.title}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span>{bookingDetails.properties?.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Check-in:</span>
                        <span>
                          {new Date(
                            bookingDetails.check_in
                          ).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Check-out:</span>
                        <span>
                          {new Date(
                            bookingDetails.check_out
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Amount:</span>
                    <span className="font-semibold">
                      ${bookingDetails.total_price}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-gray-700">
                  A confirmation email has been sent to your email address.
                  Check your inbox for all the details of your booking.
                </p>
              </div>

              <div className="flex space-x-4">
                <Button
                  onClick={() => navigate("/bookings")}
                  className="flex-1"
                >
                  View All Bookings
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate("/")}
                  className="flex-1"
                >
                  Return Home
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center my-8">
              <p className="text-gray-700">No booking details available</p>
              <Button onClick={() => navigate("/")} className="mt-4">
                Return Home
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default PaymentSuccess;
