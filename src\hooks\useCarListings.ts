import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type CarListing = {
  id: string;
  title: string;
  description: string;
  car_type: Database["public"]["Enums"]["car_type"];
  location: string;
  latitude: number | null;
  longitude: number | null;
  formatted_address: string | null;
  price_day: number;
  price_week: number;
  price_month: number;
  make: string;
  model: string;
  year: number;
  images: string[];
  transmission: string;
  seats: number;
  fuel_type: string;
  features: string[];
  is_dummy?: boolean;
};

export const useCarListings = (filters?: {
  type?: Database["public"]["Enums"]["car_type"];
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  seats?: number;
}) => {
  return useQuery({
    queryKey: ["cars", filters],
    queryFn: async () => {
      let query = supabase
        .from("cars")
        .select("*")
        .eq("status", "approved")
        .eq("is_active", true);

      // Apply filters
      if (filters?.type) {
        query = query.eq("car_type", filters.type);
      }

      if (filters?.location) {
        query = query.ilike("location", `%${filters.location}%`);
      }

      if (filters?.minPrice) {
        query = query.gte("price_day", filters.minPrice);
      }

      if (filters?.maxPrice) {
        query = query.lte("price_day", filters.maxPrice);
      }

      if (filters?.seats) {
        query = query.eq("seats", filters.seats);
      }

      const { data, error } = await query;

      if (error) throw error;

      return (data || []) as CarListing[];
    },
  });
};
