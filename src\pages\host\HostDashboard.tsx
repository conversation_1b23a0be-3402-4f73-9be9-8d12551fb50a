import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useNavigate } from "react-router-dom";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Home, CalendarDays, DollarSign } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";

const HostDashboard = () => {
  const { user, getUserRole, getUserRoles } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [properties, setProperties] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [roleCheckComplete, setRoleCheckComplete] = useState(false);

  useEffect(() => {
    // Prevent multiple calls if role check is already complete
    if (roleCheckComplete) return;

    const checkUserRole = async () => {
      if (!user) {
        navigate("/auth");
        return;
      }

      setLoading(true);

      try {
        // Use AuthProvider's getUserRoles method for consistency
        const roles = await getUserRoles();
        const role = await getUserRole();

        setUserRole(role);
        setUserRoles(roles);
        setRoleCheckComplete(true);

        // If user is not a host, redirect to profile
        if (!roles.includes("host") && role !== "admin") {
          navigate("/profile");
          return;
        }

        await fetchHostData();
      } catch (error: any) {
        console.error("Error in role check or data fetch:", error);
        toast.error("Failed to load dashboard data");
        setRoleCheckComplete(true);
        // Don't redirect on error, let user try again
      } finally {
        setLoading(false);
      }
    };

    checkUserRole();
  }, [user?.id]); // Only depend on user.id to prevent unnecessary re-renders

  const fetchHostData = async () => {
    if (!user) return;

    try {
      // Fetch host properties
      const { data: propertiesData, error: propertiesError } = await supabase
        .from("properties")
        .select("*")
        .eq("owner_id", user.id);

      if (propertiesError) throw propertiesError;
      setProperties(propertiesData || []);

      // Fetch property IDs for bookings query
      const propertyIds = propertiesData?.map((property) => property.id) || [];

      if (propertyIds.length > 0) {
        // Fetch bookings for host properties
        const { data: bookingsData, error: bookingsError } = await supabase
          .from("bookings")
          .select("*")
          .in("property_id", propertyIds)
          .order("created_at", { ascending: false });

        if (bookingsError) throw bookingsError;
        setBookings(bookingsData || []);

        // Calculate total earnings
        const earnings = bookingsData?.reduce((total, booking) => {
          return (
            total +
            (booking.status === "confirmed" ? Number(booking.total_price) : 0)
          );
        }, 0);

        setTotalEarnings(earnings || 0);
      }

      // Fetch hotels with room types for pricing calculation
      const { data: hotelsData, error: hotelsError } = await supabase
        .from("hotels")
        .select(
          `
          *,
          room_types (*)
        `
        )
        .eq("owner_id", user.id);

      // Calculate min_price for each hotel
      const hotelsWithPricing =
        !hotelsError && hotelsData
          ? hotelsData.map((hotel) => {
              const min_price =
                hotel.room_types && hotel.room_types.length > 0
                  ? Math.min(
                      ...hotel.room_types.map((rt: any) => rt.base_price)
                    )
                  : 0;

              return {
                ...hotel,
                min_price,
              };
            })
          : [];

      // Also fetch car listings if available
      const { data: carsData, error: carsError } = await supabase
        .from("cars")
        .select("*")
        .eq("owner_id", user.id);

      if (!carsError && carsData) {
        // Add car and hotel data to properties for display
        setProperties([
          ...(propertiesData || []),
          ...hotelsWithPricing,
          ...(carsData || []),
        ]);

        // Fetch car bookings
        const carIds = carsData?.map((car) => car.id) || [];

        if (carIds.length > 0) {
          const { data: carBookingsData, error: carBookingsError } =
            await supabase
              .from("car_bookings")
              .select("*")
              .in("car_id", carIds)
              .order("created_at", { ascending: false });

          if (!carBookingsError && carBookingsData) {
            // Add car bookings to total bookings
            setBookings([...(bookings || []), ...(carBookingsData || [])]);

            // Add car earnings to total earnings
            const carEarnings = carBookingsData?.reduce((total, booking) => {
              return (
                total +
                (booking.status === "confirmed"
                  ? Number(booking.total_price)
                  : 0)
              );
            }, 0);

            setTotalEarnings(totalEarnings + (carEarnings || 0));
          }
        }
      } else {
        // If no cars, still add hotels to properties
        setProperties([...(propertiesData || []), ...hotelsWithPricing]);
      }
    } catch (error: any) {
      console.error("Error fetching host data:", error);
      toast.error("Failed to load host data");
    }
  };

  if (loading || !roleCheckComplete) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  // If role check is complete but user is not authorized, show error
  if (
    roleCheckComplete &&
    !userRoles.includes("host") &&
    userRole !== "admin"
  ) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You need to be a host to access this page.
          </p>
          <button
            onClick={() => navigate("/profile")}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Host Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Listings
            </CardTitle>
            <Home className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{properties.length}</div>
            <p className="text-xs text-muted-foreground">
              Properties and vehicles you have listed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Bookings
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings.length}</div>
            <p className="text-xs text-muted-foreground">
              All-time bookings for your listings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Total Earnings
            </CardTitle>
            <DollarSign className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalEarnings.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              From confirmed bookings
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Your Listings</h2>
          <div className="space-x-2">
            <button
              onClick={() => navigate("/listings/create")}
              className="px-3 py-1 bg-black text-white rounded-md text-sm"
            >
              Add Property
            </button>
            <button
              onClick={() => navigate("/cars/create")}
              className="px-3 py-1 bg-black text-white rounded-md text-sm"
            >
              Add Car
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {properties.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  You haven't created any listings yet.
                </p>
                <div className="flex justify-center mt-4 space-x-2">
                  <button
                    onClick={() => {
                      sessionStorage.setItem("createListingFrom", "host");
                      navigate("/listings/create?from=host");
                    }}
                    className="px-3 py-1 bg-black text-white rounded-md text-sm"
                  >
                    Create Property Listing
                  </button>
                  <button
                    onClick={() => {
                      sessionStorage.setItem("createCarListingFrom", "host");
                      navigate("/cars/create?from=host");
                    }}
                    className="px-3 py-1 bg-black text-white rounded-md text-sm"
                  >
                    Create Car Listing
                  </button>
                </div>
              </CardContent>
            </Card>
          ) : (
            properties.map((property: any) => (
              <Card key={property.id} className="overflow-hidden">
                <div className="aspect-video relative">
                  <img
                    src={property.images?.[0] || "/placeholder.svg"}
                    alt={property.title || property.make}
                    className="object-cover w-full h-full"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold truncate">
                    {property.title || `${property.make} ${property.model}`}
                  </h3>
                  <p className="text-sm text-muted-foreground truncate">
                    {property.location}
                  </p>
                  <div className="mt-2 flex items-center justify-between">
                    <Badge
                      variant={
                        property.status === "active" ? "default" : "secondary"
                      }
                    >
                      {property.status}
                    </Badge>
                    <span className="font-semibold">
                      {property.price
                        ? `$${property.price}/night`
                        : property.price_day
                        ? `$${property.price_day}/day`
                        : property.min_price && property.min_price > 0
                        ? `From $${property.min_price}/night`
                        : "Contact for rates"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default HostDashboard;
