# Hotel Booking System Test Cases

## Test Category: Hotel Booking System
**Priority**: Critical
**Estimated Time**: 55 minutes

---

## TC-HOTEL-001: Create Hotel Listing
**Objective**: Verify hotel managers can create hotel listings

### Test Steps:
1. <PERSON><PERSON> as hotel manager/host
2. Navigate to "Add Hotel" or "Create Hotel Listing" page
3. Fill in hotel details:
   - Hotel Name: "Gesco Beach Resort"
   - Description: "Luxury beachfront resort with modern amenities"
   - Location: "Kololi, The Gambia"
   - Star Rating: "4"
   - Contact Information
4. Upload hotel images (exterior, lobby, facilities)
5. Set hotel amenities (Pool, Restaurant, Spa, etc.)
6. Add room types and configurations
7. Submit hotel listing

### Expected Results:
- Form accepts all hotel details
- Image upload works for different categories
- Amenities selection functions properly
- Room type management is available
- Hotel listing is created with pending status
- Hotel appears in management dashboard

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-002: Room Type Management
**Objective**: Verify hotel room types can be managed

### Test Steps:
1. <PERSON><PERSON> as hotel manager
2. Navigate to hotel management
3. Select hotel to manage room types
4. Add new room type:
   - Name: "Deluxe Ocean View"
   - Description: "Spacious room with ocean view"
   - Base Price: "200"
   - Max Occupancy: "2"
   - Bed Configuration: "1 King Bed"
5. Upload room images
6. Set room amenities
7. Configure room inventory (number of rooms)
8. Save room type configuration

### Expected Results:
- Room type form accepts all details
- Room images upload successfully
- Amenities can be selected per room type
- Inventory numbers can be set
- Room type is saved and listed
- Room type appears in booking system

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-003: Room Inventory Management
**Objective**: Verify room inventory and availability management

### Test Steps:
1. Login as hotel manager
2. Navigate to room inventory management
3. Set room inventory for different dates:
   - Standard rooms: 10 available
   - Deluxe rooms: 5 available
   - Suite rooms: 2 available
4. Block specific rooms for maintenance
5. Set seasonal pricing for peak periods
6. Save inventory configuration
7. Test from customer perspective:
   - Check room availability
   - Verify pricing changes
   - Book rooms and check inventory update

### Expected Results:
- Inventory can be set per room type and date
- Blocked rooms are unavailable for booking
- Seasonal pricing is applied correctly
- Bookings reduce available inventory
- Inventory updates in real-time
- Overbooking is prevented

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-004: Hotel Room Booking Flow
**Objective**: Verify users can book hotel rooms successfully

### Test Steps:
1. Navigate to hotel listings page
2. Select a hotel
3. Choose booking dates:
   - Check-in: Tomorrow
   - Check-out: 3 days later
4. Select number of guests
5. Choose room type from available options
6. Select number of rooms
7. Add guest information
8. Review booking summary
9. Proceed to payment
10. Complete booking with test payment
11. Verify booking confirmation

### Expected Results:
- Hotel availability is checked for dates
- Room types show correct availability
- Guest count validation works
- Booking summary is accurate
- Payment process completes successfully
- Booking confirmation is received
- Room inventory is updated

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-005: Multi-Room Booking
**Objective**: Verify booking multiple rooms in same hotel

### Test Steps:
1. Navigate to hotel booking page
2. Select booking dates
3. Choose multiple rooms:
   - 2x Standard rooms
   - 1x Deluxe room
4. Add guest information for each room
5. Verify pricing calculation for multiple rooms
6. Add special requests
7. Complete booking process
8. Verify confirmation shows all rooms
9. Check inventory deduction for all rooms

### Expected Results:
- Multiple room selection works
- Guest information can be added per room
- Pricing calculates correctly for all rooms
- Special requests are captured
- Confirmation lists all booked rooms
- Inventory is reduced for each room type

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-006: Hotel Search and Filtering
**Objective**: Verify hotel search and filter functionality

### Test Steps:
1. Navigate to hotel listings page
2. Test search functionality:
   - Search by location
   - Search by hotel name
3. Test filtering options:
   - Filter by star rating
   - Filter by price range
   - Filter by amenities (Pool, Spa, etc.)
   - Filter by room types
   - Filter by availability dates
4. Test sorting options:
   - Sort by price
   - Sort by rating
   - Sort by distance
5. Verify results accuracy

### Expected Results:
- Search returns relevant hotels
- Filters work independently and combined
- Sorting functions correctly
- Results update dynamically
- No results message when appropriate
- Filter counts are accurate

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-007: Hotel Detail Page
**Objective**: Verify hotel detail page displays all information

### Test Steps:
1. Navigate to hotel listings
2. Click on a hotel to view details
3. Verify hotel detail page shows:
   - Hotel information and description
   - Image gallery
   - Amenities and facilities
   - Room types and pricing
   - Location and contact details
   - Reviews and ratings
   - Booking widget
   - Policies (cancellation, check-in/out)
4. Test image gallery functionality
5. Test room type selection
6. Test booking widget interaction

### Expected Results:
- All hotel information displays correctly
- Image gallery functions properly
- Room types are clearly presented
- Pricing is accurate and clear
- Booking widget is functional
- Policies are clearly stated
- Page is mobile responsive

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-008: Hotel Booking Modifications
**Objective**: Verify users can modify hotel bookings

### Test Steps:
1. Create a hotel booking (future dates)
2. Login and navigate to booking management
3. Select the hotel booking to modify
4. Test modification options:
   - Change check-in/check-out dates
   - Modify number of rooms
   - Change room types (if available)
   - Update guest information
5. Calculate price differences
6. Process payment adjustment (if needed)
7. Verify booking update confirmation

### Expected Results:
- Booking modifications are allowed (within policy)
- Date changes check room availability
- Room type changes check availability
- Price adjustments are calculated correctly
- Payment differences are processed
- Updated booking confirmation is sent

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-009: Hotel Manager Dashboard
**Objective**: Verify hotel manager management features

### Test Steps:
1. Login as hotel manager
2. Navigate to hotel management dashboard
3. Verify dashboard displays:
   - Hotel overview
   - Room occupancy rates
   - Booking requests/confirmations
   - Revenue summary
   - Calendar overview
   - Recent activity
4. Test hotel management functions:
   - Edit hotel details
   - Manage room inventory
   - View booking details
   - Update pricing
5. Test reporting features

### Expected Results:
- Dashboard provides comprehensive overview
- Occupancy rates are accurate
- Booking information is current
- Revenue calculations are correct
- Management functions work properly
- Reports can be generated

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-HOTEL-010: Seasonal Pricing Management
**Objective**: Verify seasonal pricing configuration

### Test Steps:
1. Login as hotel manager
2. Navigate to pricing management
3. Set up seasonal pricing:
   - Peak season rates (December-January)
   - Off-season rates (May-September)
   - Holiday surcharges
4. Configure pricing rules:
   - Weekend vs weekday rates
   - Minimum stay requirements
   - Early booking discounts
5. Save pricing configuration
6. Test from customer perspective:
   - Check pricing for different dates
   - Verify seasonal rates apply
   - Test minimum stay enforcement

### Expected Results:
- Seasonal pricing can be configured
- Different rate types can be set
- Pricing rules are enforced
- Customer sees correct pricing
- Minimum stay requirements work
- Discounts apply correctly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
