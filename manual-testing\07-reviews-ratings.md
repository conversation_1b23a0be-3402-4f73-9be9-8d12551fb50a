# Reviews & Ratings Test Cases

## Test Category: Reviews & Ratings System
**Priority**: High
**Estimated Time**: 35 minutes

---

## TC-REV-001: Submit Property Review
**Objective**: Verify users can submit reviews for properties

### Test Steps:
1. Complete a property booking and stay
2. Navigate to the property detail page
3. Locate "Write a Review" button
4. Click "Write a Review" button
5. Fill in review form:
   - Rating: 4 stars
   - Review title: "Great stay with beautiful views"
   - Review comment: "The property was clean, well-located, and the host was very responsive. Would definitely stay again!"
6. Submit the review
7. Verify review appears on property page
8. Check if host receives notification

### Expected Results:
- Review button is visible for users who have stayed
- Review form opens correctly
- All form fields accept input
- Star rating system works
- Review is submitted successfully
- Review appears on property page
- Host receives review notification
- Review affects property's overall rating

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-002: Submit Car Rental Review
**Objective**: Verify users can review car rentals

### Test Steps:
1. Complete a car rental booking
2. Navigate to the car detail page
3. Click "Write a Review" button
4. Fill in review form:
   - Rating: 5 stars
   - Review comment: "Excellent car in perfect condition. Pickup and return process was smooth."
5. Submit the review
6. Verify review appears on car listing
7. Check review in user's review history
8. Verify car owner receives notification

### Expected Results:
- Review option is available for completed rentals
- Car review form functions properly
- Review is saved and displayed
- Review appears in user's history
- Car owner is notified
- Car's average rating is updated
- Review affects car's visibility in search

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-003: Submit Hotel Review
**Objective**: Verify users can review hotel stays

### Test Steps:
1. Complete a hotel booking and stay
2. Navigate to the hotel detail page
3. Click "Write a Review" button
4. Fill in review form:
   - Rating: 3 stars
   - Review comment: "Good location but room could be cleaner. Staff was friendly and helpful."
5. Submit the review
6. Verify review appears on hotel page
7. Check if review affects hotel rating
8. Verify hotel manager receives notification

### Expected Results:
- Hotel review option is available
- Review form accepts hotel-specific feedback
- Review is published on hotel page
- Hotel's overall rating is recalculated
- Hotel manager is notified
- Review appears in chronological order
- Review affects hotel's search ranking

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-004: Review Display and Sorting
**Objective**: Verify reviews are displayed correctly

### Test Steps:
1. Navigate to a listing with multiple reviews
2. Verify review display shows:
   - Reviewer name and avatar
   - Star rating
   - Review date
   - Review comment
   - Helpful/unhelpful votes (if implemented)
3. Test review sorting options:
   - Most recent first
   - Highest rated first
   - Lowest rated first
   - Most helpful first
4. Test review pagination (if implemented)
5. Verify average rating calculation

### Expected Results:
- All review information displays correctly
- Reviewer information is shown appropriately
- Review dates are formatted correctly
- Sorting options work as expected
- Pagination functions properly
- Average rating is calculated accurately
- Review layout is responsive

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-005: Review Validation and Restrictions
**Objective**: Verify review submission rules are enforced

### Test Steps:
1. Test review restrictions:
   - Try to review without completing booking
   - Try to submit multiple reviews for same booking
   - Try to review own property/car/hotel
2. Test review validation:
   - Submit review without rating
   - Submit review without comment
   - Submit review with inappropriate content
   - Submit extremely long review
3. Test review timing restrictions:
   - Review before stay completion
   - Review long after stay completion

### Expected Results:
- Only eligible users can submit reviews
- Duplicate reviews are prevented
- Self-reviews are blocked
- Required fields are enforced
- Inappropriate content is filtered
- Review length limits are enforced
- Timing restrictions are applied correctly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-006: Review Moderation
**Objective**: Verify review moderation system

### Test Steps:
1. Submit review with inappropriate content
2. Verify review moderation:
   - Review is flagged for moderation
   - Inappropriate content is filtered
   - Review status is tracked
3. Test admin moderation features:
   - Admin can approve/reject reviews
   - Admin can edit inappropriate reviews
   - Admin can delete spam reviews
4. Test automated moderation:
   - Profanity filtering
   - Spam detection
   - Fake review detection

### Expected Results:
- Inappropriate reviews are flagged
- Moderation workflow functions
- Admin has proper moderation tools
- Automated filters work correctly
- Review status is transparent
- Moderated reviews are handled appropriately
- Users are notified of moderation actions

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-007: Rating System Accuracy
**Objective**: Verify rating calculations are accurate

### Test Steps:
1. Create multiple reviews with different ratings:
   - Review 1: 5 stars
   - Review 2: 3 stars
   - Review 3: 4 stars
   - Review 4: 2 stars
2. Verify average rating calculation: (5+3+4+2)/4 = 3.5 stars
3. Test rating display:
   - Overall rating shows correctly
   - Star display is accurate
   - Rating count is correct
4. Test rating updates:
   - New reviews update average
   - Deleted reviews update average
   - Rating changes are immediate

### Expected Results:
- Average ratings are calculated correctly
- Rating displays are accurate
- Star visualizations work properly
- Rating counts are correct
- Updates happen in real-time
- Rating precision is appropriate
- Rating affects listing visibility

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-008: Review Response System
**Objective**: Verify hosts can respond to reviews

### Test Steps:
1. Submit a review as a guest
2. Login as the host/owner
3. Navigate to review management
4. Find the submitted review
5. Click "Respond to Review"
6. Write response: "Thank you for your feedback. We're glad you enjoyed your stay!"
7. Submit the response
8. Verify response appears under review
9. Check if guest receives notification

### Expected Results:
- Hosts can see reviews of their listings
- Response option is available
- Response form functions correctly
- Response is published under review
- Guest is notified of response
- Response appears in chronological order
- Response affects overall listing perception

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-009: Review Analytics and Insights
**Objective**: Verify review analytics for hosts

### Test Steps:
1. Login as host with multiple reviews
2. Navigate to review analytics/insights
3. Verify analytics show:
   - Average rating over time
   - Review count trends
   - Rating distribution
   - Common keywords/themes
   - Response rate to reviews
4. Test filtering options:
   - By date range
   - By property/car/hotel
   - By rating level
5. Test export functionality (if available)

### Expected Results:
- Analytics provide meaningful insights
- Charts and graphs display correctly
- Filtering options work properly
- Data is accurate and up-to-date
- Export functionality works
- Insights help hosts improve
- Analytics are mobile-friendly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-REV-010: Review Impact on Search and Ranking
**Objective**: Verify reviews affect search results

### Test Steps:
1. Create listings with different review scores:
   - Listing A: 4.8 stars (10 reviews)
   - Listing B: 3.2 stars (5 reviews)
   - Listing C: 4.5 stars (20 reviews)
2. Search for listings in same area
3. Verify search ranking considers:
   - Average rating
   - Number of reviews
   - Recent review activity
4. Test filtering by rating:
   - 4+ stars only
   - 3+ stars only
5. Verify highly-rated listings get priority

### Expected Results:
- Higher-rated listings rank better
- Review count affects ranking
- Recent reviews have impact
- Rating filters work correctly
- Search algorithm considers reviews
- Quality listings get visibility
- Poor ratings affect discoverability

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
