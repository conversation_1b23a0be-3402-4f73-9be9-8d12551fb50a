-- Create initial admin user if none exists
-- This script creates a default admin user for testing purposes

DO $$
DECLARE
  admin_count INTEGER;
  admin_id UUID;
BEGIN
  -- Check if any admin users exist
  SELECT COUNT(*) INTO admin_count FROM public.admin_users;
  
  -- Only create admin if none exist
  IF admin_count = 0 THEN
    -- Create initial super admin using the function
    SELECT create_initial_super_admin(
      '<EMAIL>',
      'admin123',  -- Default password - CHAN<PERSON> THIS IN PRODUCTION!
      'Admin',
      'User'
    ) INTO admin_id;
    
    RAISE NOTICE 'Created initial admin user with ID: %', admin_id;
    RAISE NOTICE 'Email: <EMAIL>';
    RAISE NOTICE 'Password: admin123';
    RAISE NOTICE 'IMPORTANT: Change the default password immediately!';
  ELSE
    RAISE NOTICE 'Admin users already exist. Skipping creation.';
  END IF;
END $$;
