import { useEffect, useState } from "react";
import { useAuth } from "./AuthProvider";
import { useNavigate, Link, Outlet, useLocation } from "react-router-dom";
import {
  Home,
  CalendarDays,
  DollarSign,
  MessageSquare,
  Settings,
  List,
} from "lucide-react";
import { Database } from "@/integrations/supabase/types";
import Navbar from "./Navbar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

type UserRole = Database["public"]["Enums"]["user_role"];

const HostLayout = () => {
  const { user, getUserRole } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [isHost, setIsHost] = useState(false);

  const isActiveRoute = (path: string) => {
    if (path === "/host") {
      return location.pathname === "/host";
    }
    return location.pathname.startsWith(path);
  };

  useEffect(() => {
    const checkHostStatus = async () => {
      if (!user) {
        navigate("/auth");
        return;
      }

      try {
        const role = await getUserRole();

        if (role !== "host" && role !== "admin") {
          navigate("/");
          return;
        }

        setIsHost(true);
      } catch (error) {
        console.error("Error checking host status:", error);
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    checkHostStatus();
  }, [user, navigate, getUserRole]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  if (!isHost) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Standard Navbar */}
      <Navbar />

      <div className="flex-1 flex">
        {/* Fixed Left Sidebar */}
        <aside className="fixed left-0 top-16 h-[calc(100vh-4rem)] w-16 lg:w-64 bg-white shadow-lg border-r border-gray-200 z-40 transition-all duration-300 ease-in-out">
          <nav className="h-full flex flex-col">
            {/* Brand Section */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-warm-tan to-soft-orange rounded-lg flex items-center justify-center flex-shrink-0">
                  <Home className="h-4 w-4 text-white" />
                </div>
                <span className="ml-3 text-lg font-semibold text-gray-900 hidden lg:block">
                  Host Panel
                </span>
              </div>
            </div>

            <TooltipProvider>
              <ul className="space-y-1 flex-1 p-2 lg:p-4">
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <Home
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Dashboard
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Dashboard</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host/listings"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host/listings")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <List
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host/listings")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host/listings")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Listings
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Listings</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host/reservations"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host/reservations")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <CalendarDays
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host/reservations")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host/reservations")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Reservations
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Reservations</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host/earnings"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host/earnings")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <DollarSign
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host/earnings")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host/earnings")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Payments & Earnings
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Payments & Earnings</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host/messages"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host/messages")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <MessageSquare
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host/messages")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host/messages")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Messages
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Messages</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
                <li>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link
                        to="/host/settings"
                        className={`flex items-center px-3 py-3 rounded-lg transition-all duration-200 group ${
                          isActiveRoute("/host/settings")
                            ? "bg-warm-tan/10 text-dark-brown border-r-2 border-warm-tan"
                            : "hover:bg-gray-100"
                        }`}
                      >
                        <Settings
                          className={`h-5 w-5 flex-shrink-0 ${
                            isActiveRoute("/host/settings")
                              ? "text-warm-tan"
                              : "text-gray-600 group-hover:text-gray-900"
                          }`}
                        />
                        <span
                          className={`ml-3 text-sm font-medium hidden lg:block ${
                            isActiveRoute("/host/settings")
                              ? "text-dark-brown"
                              : "text-gray-700 group-hover:text-gray-900"
                          }`}
                        >
                          Settings
                        </span>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="lg:hidden">
                      <p>Settings</p>
                    </TooltipContent>
                  </Tooltip>
                </li>
              </ul>
            </TooltipProvider>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 ml-16 lg:ml-64 transition-all duration-300 ease-in-out">
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default HostLayout;
