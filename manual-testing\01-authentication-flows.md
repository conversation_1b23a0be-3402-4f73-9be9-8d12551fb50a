# Authentication & User Management Test Cases

## Test Category: Authentication Flows
**Priority**: Critical
**Estimated Time**: 45 minutes

---

## TC-AUTH-001: Email Registration Flow
**Objective**: Verify users can register using email address

### Test Steps:
1. Navigate to registration page
2. Select "Email" as primary login method
3. Fill in registration form:
   - First Name: "<PERSON>"
   - Last Name: "Doe"
   - Mobile Number: "+**********"
   - Email: "<EMAIL>"
   - Password: "Password123!"
   - Confirm Password: "Password123!"
4. Click "Sign Up" button
5. Check for redirect to OTP verification page
6. Verify email received with OTP code
7. Enter OTP code and submit
8. Verify successful registration and redirect

### Expected Results:
- Registration form accepts all valid inputs
- OTP verification page displays with email method
- Email with OTP code is received
- Successful verification redirects to dashboard/home
- User profile is created with correct information

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-002: Phone Registration Flow
**Objective**: Verify users can register using phone number

### Test Steps:
1. Navigate to registration page
2. Select "Phone" as primary login method
3. Fill in registration form:
   - First Name: "Jane"
   - Last Name: "Smith"
   - Mobile Number: "+**********"
   - Email: "<EMAIL>" (optional)
   - Password: "Password123!"
   - Confirm Password: "Password123!"
4. Click "Sign Up" button
5. Check for redirect to OTP verification page
6. Verify SMS received with OTP code
7. Enter OTP code and submit
8. Verify successful registration and redirect

### Expected Results:
- Registration form accepts phone as primary method
- OTP verification page displays with phone method
- SMS with OTP code is received
- Successful verification creates user account
- User can access protected pages

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-003: Email Login Flow
**Objective**: Verify existing users can login with email

### Test Steps:
1. Navigate to login page
2. Select "Email" login method
3. Enter email: "<EMAIL>"
4. Enter password: "Password123!"
5. Click "Sign In" button
6. Verify successful login and redirect

### Expected Results:
- Login form accepts email credentials
- Successful authentication occurs
- User is redirected to appropriate page
- User session is established

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-004: Phone Login Flow
**Objective**: Verify existing users can login with phone

### Test Steps:
1. Navigate to login page
2. Select "Phone" login method
3. Enter phone: "+**********"
4. Enter password: "Password123!"
5. Click "Sign In" button
6. Verify successful login and redirect

### Expected Results:
- Login form accepts phone credentials
- Successful authentication occurs
- User is redirected to appropriate page
- User session is established

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-005: Password Reset Flow
**Objective**: Verify users can reset forgotten passwords

### Test Steps:
1. Navigate to login page
2. Click "Forgot Password?" link
3. Enter email address: "<EMAIL>"
4. Click "Send Reset Link" button
5. Check email for reset instructions
6. Click reset link in email
7. Enter new password: "NewPassword123!"
8. Confirm new password: "NewPassword123!"
9. Submit password reset
10. Attempt login with new password

### Expected Results:
- Reset link is sent to email
- Reset page loads from email link
- New password is accepted and saved
- User can login with new password
- Old password no longer works

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-006: OTP Verification - Email
**Objective**: Verify OTP verification works for email registration

### Test Steps:
1. Complete email registration (TC-AUTH-001 steps 1-4)
2. On OTP verification page, verify:
   - Email address is displayed correctly
   - "Resend Code" button is available
   - Input field accepts 6-digit code
3. Enter incorrect OTP code
4. Verify error message displays
5. Click "Resend Code" button
6. Enter correct OTP code
7. Verify successful verification

### Expected Results:
- OTP page shows correct email
- Invalid OTP shows error message
- Resend functionality works
- Valid OTP completes verification
- User is redirected after verification

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-007: OTP Verification - Phone
**Objective**: Verify OTP verification works for phone registration

### Test Steps:
1. Complete phone registration (TC-AUTH-002 steps 1-4)
2. On OTP verification page, verify:
   - Phone number is displayed correctly
   - "Resend Code" button is available
   - Input field accepts 6-digit code
3. Enter incorrect OTP code
4. Verify error message displays
5. Click "Resend Code" button
6. Enter correct OTP code
7. Verify successful verification

### Expected Results:
- OTP page shows correct phone number
- Invalid OTP shows error message
- Resend functionality works
- Valid OTP completes verification
- User is redirected after verification

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-008: Logout Flow
**Objective**: Verify users can logout successfully

### Test Steps:
1. Login as any user
2. Navigate to user menu/profile
3. Click "Logout" or "Sign Out" button
4. Verify logout confirmation (if any)
5. Confirm logout action
6. Verify redirect to home/login page
7. Attempt to access protected page
8. Verify redirect to login page

### Expected Results:
- Logout option is accessible
- User session is terminated
- Redirect to public page occurs
- Protected pages require re-authentication
- User data is cleared from browser

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-009: Unverified User Login Attempt
**Objective**: Verify unverified users are guided to verification

### Test Steps:
1. Register new user but don't complete OTP verification
2. Attempt to login with unverified credentials
3. Verify appropriate error message
4. Check if verification flow is offered
5. Complete verification if prompted
6. Retry login after verification

### Expected Results:
- Unverified users cannot login
- Clear error message about verification
- Option to complete verification is provided
- Login works after verification is complete

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-AUTH-010: Session Management
**Objective**: Verify user sessions are managed properly

### Test Steps:
1. Login as user
2. Open new browser tab
3. Navigate to protected page
4. Verify user remains logged in
5. Close browser completely
6. Reopen browser and navigate to site
7. Check if user session persists
8. Wait for session timeout (if applicable)
9. Verify session expiration handling

### Expected Results:
- Session persists across tabs
- Session behavior matches configuration
- Expired sessions require re-authentication
- Session data is secure

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
