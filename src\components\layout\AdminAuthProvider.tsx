import React, { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";

interface AdminUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: string;
  is_active: boolean;
  created_at: string;
  last_login_at?: string;
}

interface AdminAuthContextType {
  adminUser: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(
  undefined
);

export const useAdminAuth = () => {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error("useAdminAuth must be used within an AdminAuthProvider");
  }
  return context;
};

interface AdminAuthProviderProps {
  children: React.ReactNode;
}

export const AdminAuthProvider: React.FC<AdminAuthProviderProps> = ({
  children,
}) => {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for existing admin session on mount
  useEffect(() => {
    checkAdminSession();
  }, []);

  const checkAdminSession = async () => {
    try {
      const sessionToken = localStorage.getItem("admin_session_token");
      if (!sessionToken) {
        setLoading(false);
        return;
      }

      // Validate the session token using the database function
      const { data, error } = await supabase.rpc("validate_admin_session", {
        token: sessionToken,
      });

      if (error || !data || data.length === 0) {
        // Invalid session, clear it
        localStorage.removeItem("admin_session_token");
        setAdminUser(null);
        setLoading(false);
        return;
      }

      // Valid session, set admin user
      const adminData = data[0];
      setAdminUser({
        id: adminData.id,
        email: adminData.email,
        first_name: adminData.first_name,
        last_name: adminData.last_name,
        role: adminData.role,
        is_active: adminData.is_active,
        created_at: adminData.created_at,
        last_login_at: adminData.last_login_at,
      });
    } catch (error) {
      console.error("Error checking admin session:", error);
      localStorage.removeItem("admin_session_token");
      setAdminUser(null);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // Use the admin login function
      const { data, error } = await supabase.rpc("admin_login", {
        admin_email: email,
        admin_password: password,
      });

      if (error) {
        throw new Error(error.message || "Invalid credentials");
      }

      if (!data || data.length === 0) {
        throw new Error("Invalid credentials");
      }

      const adminData = data[0];

      // Store the session token
      localStorage.setItem("admin_session_token", adminData.session_token);

      // Set admin user data (all data is returned from the function)
      setAdminUser({
        id: adminData.admin_id,
        email: adminData.email,
        first_name: adminData.first_name,
        last_name: adminData.last_name,
        role: adminData.role,
        is_active: adminData.is_active,
        created_at: adminData.created_at,
        last_login_at: adminData.last_login_at,
      });
    } catch (error: any) {
      console.error("Admin login error:", error);
      throw new Error(error.message || "Login failed");
    }
  };

  const signOut = async () => {
    try {
      const sessionToken = localStorage.getItem("admin_session_token");
      if (sessionToken) {
        // Logout using the database function
        await supabase.rpc("logout_admin", {
          token: sessionToken,
        });
      }
    } catch (error) {
      console.error("Error during admin logout:", error);
    } finally {
      // Always clear local state and storage
      localStorage.removeItem("admin_session_token");
      setAdminUser(null);
    }
  };

  const value: AdminAuthContextType = {
    adminUser,
    loading,
    signIn,
    signOut,
    isAuthenticated: !!adminUser,
  };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};
