import React, { useRef, useEffect, useState, useCallback } from "react";
import { GoogleMapsProvider } from "./GoogleMapsProvider";
import {
  getDefaultMapOptions,
  DEFAULT_CENTER,
  LocationData,
  getCurrentLocation,
  reverseGeocode,
  isGoogleMapsLoaded,
} from "@/lib/google-maps";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MapPin, Navigation, Search } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface LocationPickerProps {
  onLocationSelect: (location: LocationData) => void;
  initialLocation?: LocationData;
  height?: string;
  className?: string;
  showSearch?: boolean;
  showCurrentLocationButton?: boolean;
}

const LocationPickerMap: React.FC<LocationPickerProps> = ({
  onLocationSelect,
  initialLocation,
  height = "400px",
  className = "",
  showSearch = true,
  showCurrentLocationButton = true,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<
    google.maps.marker.AdvancedMarkerElement | google.maps.Marker | null
  >(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const [searchValue, setSearchValue] = useState(
    initialLocation?.formatted_address || ""
  );
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize map
  const initializeMap = useCallback(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    const center = initialLocation
      ? { lat: initialLocation.latitude, lng: initialLocation.longitude }
      : DEFAULT_CENTER;

    const mapOptions: google.maps.MapOptions = {
      ...getDefaultMapOptions(),
      center,
      zoom: initialLocation ? 15 : 10,
    };

    mapInstanceRef.current = new google.maps.Map(mapRef.current, mapOptions);

    // Add click listener to map
    mapInstanceRef.current.addListener("click", handleMapClick);

    // Initialize marker if initial location is provided
    if (initialLocation) {
      addMarker(initialLocation.latitude, initialLocation.longitude);
      setSearchValue(initialLocation.formatted_address);
    }

    // Initialize autocomplete if search is enabled
    if (showSearch && searchInputRef.current) {
      initializeAutocomplete();
    }
  }, [initialLocation, showSearch]);

  // Initialize autocomplete
  const initializeAutocomplete = useCallback(() => {
    if (!searchInputRef.current || autocompleteRef.current) return;

    try {
      // Use the stable Autocomplete API (works with existing Places API)
      autocompleteRef.current = new google.maps.places.Autocomplete(
        searchInputRef.current,
        {
          types: ["establishment", "geocode"],
          fields: ["place_id", "geometry", "formatted_address", "name"],
        }
      );

      // Add event listener for place selection
      autocompleteRef.current.addListener("place_changed", handlePlaceSelect);
    } catch (error) {
      console.error("Error initializing autocomplete:", error);
      setError(
        "Failed to initialize location search. Please try refreshing the page."
      );
    }
  }, []);

  // Handle map click
  const handleMapClick = useCallback(
    async (event: google.maps.MapMouseEvent) => {
      if (!event.latLng) return;

      const lat = event.latLng.lat();
      const lng = event.latLng.lng();

      addMarker(lat, lng);

      // Reverse geocode to get address
      const geocoder = new google.maps.Geocoder();
      try {
        const address = await reverseGeocode(lat, lng, geocoder);
        const locationData: LocationData = {
          latitude: lat,
          longitude: lng,
          formatted_address: address || `${lat}, ${lng}`,
        };

        setSearchValue(locationData.formatted_address);
        onLocationSelect(locationData);
      } catch (error) {
        console.error("Error reverse geocoding:", error);
        const locationData: LocationData = {
          latitude: lat,
          longitude: lng,
          formatted_address: `${lat}, ${lng}`,
        };
        onLocationSelect(locationData);
      }
    },
    [onLocationSelect]
  );

  // Handle place selection from autocomplete
  const handlePlaceSelect = useCallback(() => {
    if (!autocompleteRef.current) return;

    try {
      const place = autocompleteRef.current.getPlace();
      if (!place.geometry?.location) return;

      const lat = place.geometry.location.lat();
      const lng = place.geometry.location.lng();
      const address = place.formatted_address || place.name || `${lat}, ${lng}`;

      addMarker(lat, lng);

      // Center map on selected place
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setCenter({ lat, lng });
        mapInstanceRef.current.setZoom(15);
      }

      const locationData: LocationData = {
        latitude: lat,
        longitude: lng,
        formatted_address: address,
        place_id: place.place_id,
      };

      setSearchValue(address);
      onLocationSelect(locationData);
    } catch (error) {
      console.error("Error handling place selection:", error);
      setError("Failed to select location. Please try again.");
    }
  }, [onLocationSelect]);

  // Add marker to map
  const addMarker = useCallback(
    (lat: number, lng: number) => {
      if (!mapInstanceRef.current) return;

      try {
        // Remove existing marker
        if (markerRef.current) {
          try {
            if (
              "setMap" in markerRef.current &&
              typeof markerRef.current.setMap === "function"
            ) {
              markerRef.current.setMap(null);
            } else if ("map" in markerRef.current) {
              markerRef.current.map = null;
            }
            // Clear the reference
            markerRef.current = null;
          } catch (error) {
            console.warn("Error removing marker:", error);
            markerRef.current = null;
          }
        }

        // Try to use AdvancedMarkerElement first, fallback to regular Marker
        if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
          // Create custom marker element
          const markerElement = document.createElement("div");
          markerElement.innerHTML = `
          <div style="
            width: 32px;
            height: 32px;
            background: #DC2626;
            border: 3px solid white;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          ">
            <div style="
              width: 8px;
              height: 8px;
              background: white;
              border-radius: 50%;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(45deg);
            "></div>
          </div>
        `;

          // Add new marker using AdvancedMarkerElement
          markerRef.current = new google.maps.marker.AdvancedMarkerElement({
            position: { lat, lng },
            map: mapInstanceRef.current,
            content: markerElement,
            title: "Selected Location",
            gmpDraggable: true,
          });
        } else {
          // Fallback to regular Marker
          markerRef.current = new google.maps.Marker({
            position: { lat, lng },
            map: mapInstanceRef.current,
            title: "Selected Location",
            draggable: true,
          }) as any;
        }

        // Add drag listener to marker
        markerRef.current.addListener("dragend", async (event: any) => {
          const position = event.latLng || markerRef.current?.position;
          if (!position) return;

          const newLat =
            typeof position.lat === "function" ? position.lat() : position.lat;
          const newLng =
            typeof position.lng === "function" ? position.lng() : position.lng;

          // Reverse geocode new position
          const geocoder = new google.maps.Geocoder();
          try {
            const address = await reverseGeocode(newLat, newLng, geocoder);
            const locationData: LocationData = {
              latitude: newLat,
              longitude: newLng,
              formatted_address: address || `${newLat}, ${newLng}`,
            };

            setSearchValue(locationData.formatted_address);
            onLocationSelect(locationData);
          } catch (error) {
            console.error("Error reverse geocoding:", error);
          }
        });
      } catch (error) {
        console.error("Error creating marker:", error);
        setError("Failed to create map marker. Please try again.");
      }
    },
    [onLocationSelect]
  );

  // Get current location
  const handleGetCurrentLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    setError(null);

    try {
      const position = await getCurrentLocation();
      const lat = position.coords.latitude;
      const lng = position.coords.longitude;

      addMarker(lat, lng);

      // Center map on current location
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setCenter({ lat, lng });
        mapInstanceRef.current.setZoom(15);
      }

      // Reverse geocode to get address
      const geocoder = new google.maps.Geocoder();
      const address = await reverseGeocode(lat, lng, geocoder);

      const locationData: LocationData = {
        latitude: lat,
        longitude: lng,
        formatted_address: address || `${lat}, ${lng}`,
      };

      setSearchValue(locationData.formatted_address);
      onLocationSelect(locationData);
    } catch (error: any) {
      setError(error.message || "Failed to get current location");
    } finally {
      setIsLoadingLocation(false);
    }
  }, [onLocationSelect]);

  // Initialize map when component mounts
  useEffect(() => {
    if (isGoogleMapsLoaded()) {
      initializeMap();
    }
  }, [initializeMap]);

  // Update search value when initialLocation changes
  useEffect(() => {
    if (initialLocation?.formatted_address) {
      setSearchValue(initialLocation.formatted_address);
    }
  }, [initialLocation]);

  return (
    <div className={`space-y-4 ${className}`}>
      {showSearch && (
        <div className="space-y-2">
          <Label htmlFor="location-search">Search for a location</Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 z-10" />
              <Input
                ref={searchInputRef}
                id="location-search"
                type="text"
                placeholder="Enter an address or place name..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="pl-10"
              />
            </div>
            {showCurrentLocationButton && (
              <Button
                type="button"
                variant="outline"
                onClick={handleGetCurrentLocation}
                disabled={isLoadingLocation}
                className="flex items-center gap-2"
              >
                <Navigation className="h-4 w-4" />
                {isLoadingLocation ? "Getting..." : "Current"}
              </Button>
            )}
          </div>
        </div>
      )}

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-700">{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label>Click on the map to select a location</Label>
        <div
          ref={mapRef}
          style={{ height }}
          className="w-full border border-gray-300 rounded-lg"
        />
        <div className="space-y-1">
          <p className="text-sm text-gray-600">
            <MapPin className="inline h-4 w-4 mr-1" />
            You can drag the marker to fine-tune the location
          </p>
          <p className="text-xs text-gray-500">
            Note: Selecting a location on the map will not override your
            manually entered location name above
          </p>
        </div>
      </div>
    </div>
  );
};

export const LocationPicker: React.FC<LocationPickerProps> = (props) => {
  return (
    <GoogleMapsProvider>
      <LocationPickerMap {...props} />
    </GoogleMapsProvider>
  );
};
