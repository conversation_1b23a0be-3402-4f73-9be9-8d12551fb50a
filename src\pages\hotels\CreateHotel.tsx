import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { LocationPicker } from "@/components/maps/LocationPicker";
import HotelAmenitiesSelector from "@/components/hotels/HotelAmenitiesSelector";
import RoomTypeManager from "@/components/hotels/RoomTypeManager";
import ImageUploader from "@/components/ui/ImageUploader";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  check_in_time: z.string().default("15:00"),
  check_out_time: z.string().default("11:00"),
  images: z.any().optional(),
});

type FormData = z.infer<typeof formSchema>;

const CreateHotel = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
    formatted_address: string;
  } | null>(null);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [createdHotelId, setCreatedHotelId] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<"hotel" | "rooms">("hotel");
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(0);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      check_in_time: "15:00",
      check_out_time: "11:00",
    },
  });

  const onSubmit = async (data: FormData) => {
    if (!user) {
      toast.error("You must be logged in to create a hotel listing.");
      return;
    }

    // Additional validation for required fields
    if (!selectedLocation) {
      toast.error("Please select a location on the map.");
      return;
    }

    if (selectedImages.length === 0) {
      toast.error("Please upload at least one image of your hotel.");
      return;
    }

    if (selectedAmenities.length === 0) {
      toast.error("Please select at least one amenity for your hotel.");
      return;
    }

    setIsSubmitting(true);

    try {
      // First, ensure user has a valid profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("id", user.id)
        .single();

      if (profileError || !profile) {
        console.error("Profile check failed:", profileError);

        // Try to create profile if it doesn't exist
        const { error: createProfileError } = await supabase.rpc(
          "handle_new_user_manual",
          {
            user_id: user.id,
            first_name: user.user_metadata?.first_name || "",
            last_name: user.user_metadata?.last_name || "",
            email: user.email || user.user_metadata?.email || "",
            phone_number: user.phone || user.user_metadata?.phone_number || "",
          }
        );

        if (createProfileError) {
          console.error("Failed to create profile:", createProfileError);
          toast.error("Unable to create your profile. Please contact support.");
          return;
        }
      }
      let imageUrls: string[] = [];

      // Upload images if provided
      if (selectedImages.length > 0) {
        const uploadPromises = selectedImages.map(async (file: File) => {
          const fileExt = file.name.split(".").pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `hotels/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from("hotel-images")
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          const {
            data: { publicUrl },
          } = supabase.storage.from("hotel-images").getPublicUrl(filePath);

          return publicUrl;
        });

        imageUrls = await Promise.all(uploadPromises);

        // Reorder images to put thumbnail first
        if (thumbnailIndex > 0 && thumbnailIndex < imageUrls.length) {
          const thumbnailUrl = imageUrls[thumbnailIndex];
          imageUrls.splice(thumbnailIndex, 1);
          imageUrls.unshift(thumbnailUrl);
        }
      }

      // Create the hotel listing
      const { data: hotelData, error } = await supabase
        .from("hotels")
        .insert({
          title: data.title,
          description: data.description,
          location: data.location,
          latitude: selectedLocation?.latitude || null,
          longitude: selectedLocation?.longitude || null,
          formatted_address: selectedLocation?.formatted_address || null,
          check_in_time: data.check_in_time,
          check_out_time: data.check_out_time,
          owner_id: user.id,
          images: imageUrls,
          amenities: selectedAmenities,
          policies: {},
          status: "pending", // Explicitly set to pending for admin approval
        })
        .select()
        .single();

      if (error) throw error;

      setCreatedHotelId(hotelData.id);
      setCurrentStep("rooms");

      // Invalidate listing caches to reflect new listing
      queryClient.invalidateQueries({ queryKey: ["hotels"] });

      toast.success(
        "Hotel submitted for approval! Now add room types to complete your hotel listing. Your hotel will be reviewed by admin before going live.",
        { duration: 5000 } // Auto-dismiss after 5 seconds
      );
    } catch (error: any) {
      console.error("Hotel creation error:", error);

      let errorMessage = "Something went wrong. Please try again.";

      if (error?.message) {
        if (error.message.includes("foreign key constraint")) {
          errorMessage = "Account setup incomplete. Please contact support.";
        } else if (error.message.includes("permission denied")) {
          errorMessage =
            "You don't have permission to create listings. Please verify your account.";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Create Hotel Listing</CardTitle>
            <CardDescription>
              {currentStep === "hotel"
                ? "Provide information about your hotel to attract guests"
                : "Add room types and manage your hotel inventory"}
            </CardDescription>
            {currentStep === "hotel" && (
              <div className="mt-4 p-3 bg-warm-tan/10 border border-warm-tan/30 rounded-lg">
                <p className="text-sm text-dark-brown">
                  <strong>📋 Approval Process:</strong> Your hotel listing will
                  be reviewed by our admin team before going live. This
                  typically takes 24-48 hours.
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <Tabs
              value={currentStep}
              onValueChange={(value) =>
                setCurrentStep(value as "hotel" | "rooms")
              }
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="hotel">Hotel Information</TabsTrigger>
                <TabsTrigger value="rooms" disabled={!createdHotelId}>
                  Room Types
                </TabsTrigger>
              </TabsList>

              <TabsContent value="hotel" className="mt-6">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Hotel Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Grand Hotel Banjul"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe your hotel, its unique features, and what makes it special..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Banjul, The Gambia"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="check_in_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Check-in Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="check_out_time"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Check-out Time</FormLabel>
                            <FormControl>
                              <Input type="time" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Hotel Location on Map
                      </label>
                      <LocationPicker
                        onLocationSelect={(location) => {
                          setSelectedLocation(location);
                          // Never override manually entered location text
                          // Only update if the location field is completely empty
                          const currentLocation = form.getValues("location");
                          if (
                            !currentLocation ||
                            currentLocation.trim() === ""
                          ) {
                            form.setValue(
                              "location",
                              location.formatted_address ||
                                `${location.latitude}, ${location.longitude}`
                            );
                          }
                        }}
                        initialLocation={selectedLocation}
                        height="400px"
                        showSearch={true}
                        showCurrentLocationButton={true}
                      />
                      {selectedLocation && (
                        <p className="text-sm text-gray-600">
                          Selected: {selectedLocation.formatted_address}
                        </p>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">
                          Hotel Images
                        </label>
                        <ImageUploader
                          onImagesChange={setSelectedImages}
                          onThumbnailChange={setThumbnailIndex}
                          maxImages={10}
                          className="mt-2"
                        />
                      </div>
                    </div>

                    <Separator />

                    <HotelAmenitiesSelector
                      selectedAmenities={selectedAmenities}
                      onAmenitiesChange={setSelectedAmenities}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-accent hover:bg-accent/90"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating hotel...
                        </>
                      ) : (
                        "Create Hotel & Continue to Room Types"
                      )}
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="rooms" className="mt-6">
                {createdHotelId ? (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold mb-2">
                        Add Room Types
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Create different room categories for your hotel. You can
                        add multiple room types with different pricing and
                        amenities.
                      </p>
                    </div>

                    <RoomTypeManager hotelId={createdHotelId} />

                    <div className="flex justify-center space-x-4 pt-6 border-t">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Navigate back to host listings if user came from host area
                          const searchParams = new URLSearchParams(
                            window.location.search
                          );
                          const fromHost =
                            searchParams.get("from") === "host" ||
                            sessionStorage.getItem("createHotelFrom") ===
                              "host";

                          if (fromHost) {
                            navigate("/host/listings");
                          } else {
                            navigate("/hotels");
                          }

                          // Clear the session storage
                          sessionStorage.removeItem("createHotelFrom");
                        }}
                      >
                        Finish Later
                      </Button>
                      <Button
                        onClick={() => {
                          // Navigate back to host listings if user came from host area
                          const searchParams = new URLSearchParams(
                            window.location.search
                          );
                          const fromHost =
                            searchParams.get("from") === "host" ||
                            sessionStorage.getItem("createHotelFrom") ===
                              "host";

                          if (fromHost) {
                            navigate("/host/listings");
                          } else {
                            navigate("/hotels");
                          }

                          // Clear the session storage
                          sessionStorage.removeItem("createHotelFrom");
                        }}
                        className="bg-accent hover:bg-accent/90"
                      >
                        Complete & Go to Listings
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">
                      Please complete the hotel information first.
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default CreateHotel;
