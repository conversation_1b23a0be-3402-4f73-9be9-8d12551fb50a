-- Fix Hotel Admin Policies
-- This script adds missing admin policies for hotels table to allow admins to approve/reject hotels

-- First, check if admin policies already exist (run this to see current policies)
-- SELECT policyname FROM pg_policies WHERE tablename = 'hotels';

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Ad<PERSON> can view all hotels" ON public.hotels;
DROP POLICY IF EXISTS "Ad<PERSON> can update all hotels" ON public.hotels;

-- Add missing admin policies for hotels (same pattern as cars and properties)
CREATE POLICY "Admins can view all hotels" ON public.hotels
  FOR SELECT USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

CREATE POLICY "Admins can update all hotels" ON public.hotels
  FOR UPDATE USING (is_admin(auth.uid()) OR (owner_id = auth.uid()));

-- Verify the policies were created
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  CASE 
    WHEN policyname LIKE '%admin%' THEN '✅ ADMIN POLICY'
    WHEN policyname LIKE '%owner%' THEN '👤 OWNER POLICY'
    ELSE '📋 OTHER POLICY'
  END as policy_type
FROM pg_policies 
WHERE tablename = 'hotels' 
ORDER BY policyname;
