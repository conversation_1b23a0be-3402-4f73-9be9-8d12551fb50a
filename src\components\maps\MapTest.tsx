import React, { useState } from 'react';
import { LocationPicker } from './LocationPicker';
import { MapDisplay } from './MapDisplay';
import { LocationData } from '@/lib/google-maps';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

/**
 * Test component for Google Maps integration
 * This component can be used to test the LocationPicker and MapDisplay components
 * 
 * To use this component, add it to a route or import it in a page:
 * import { MapTest } from '@/components/maps/MapTest';
 */
export const MapTest: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [testLocations] = useState<LocationData[]>([
    {
      latitude: -1.2921,
      longitude: 36.8219,
      formatted_address: "Nairobi, Kenya",
    },
    {
      latitude: 13.4549,
      longitude: -16.5790,
      formatted_address: "Banjul, The Gambia",
    },
    {
      latitude: 6.5244,
      longitude: 3.3792,
      formatted_address: "Lagos, Nigeria",
    },
  ]);

  const handleLocationSelect = (location: LocationData) => {
    setSelectedLocation(location);
  };

  const loadTestLocation = (location: LocationData) => {
    setSelectedLocation(location);
  };

  const clearLocation = () => {
    setSelectedLocation(null);
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Google Maps Integration Test</h1>
        <p className="text-gray-600">
          Test the LocationPicker and MapDisplay components
        </p>
      </div>

      {/* Test Location Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Test Locations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {testLocations.map((location, index) => (
              <Button
                key={index}
                variant="outline"
                onClick={() => loadTestLocation(location)}
                className="text-sm"
              >
                {location.formatted_address}
              </Button>
            ))}
            <Button
              variant="outline"
              onClick={clearLocation}
              className="text-sm"
            >
              Clear Location
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Location Picker Test */}
      <Card>
        <CardHeader>
          <CardTitle>Location Picker Test</CardTitle>
        </CardHeader>
        <CardContent>
          <LocationPicker
            onLocationSelect={handleLocationSelect}
            initialLocation={selectedLocation || undefined}
            height="400px"
            showSearch={true}
            showCurrentLocationButton={true}
          />
          
          {selectedLocation && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Selected Location:</h4>
              <div className="space-y-1 text-sm">
                <p><strong>Address:</strong> {selectedLocation.formatted_address}</p>
                <p><strong>Latitude:</strong> {selectedLocation.latitude}</p>
                <p><strong>Longitude:</strong> {selectedLocation.longitude}</p>
                {selectedLocation.place_id && (
                  <p><strong>Place ID:</strong> {selectedLocation.place_id}</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* Map Display Test */}
      {selectedLocation && (
        <Card>
          <CardHeader>
            <CardTitle>Map Display Test</CardTitle>
          </CardHeader>
          <CardContent>
            <MapDisplay
              location={selectedLocation}
              title="Test Location"
              height="350px"
              showNavigationButtons={true}
              showInfoWindow={true}
            />
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">1. Test Location Picker:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Use the search box to find a location</li>
              <li>Click "Current" to get your current location (requires HTTPS)</li>
              <li>Click anywhere on the map to place a marker</li>
              <li>Drag the marker to adjust the position</li>
              <li>Verify that the location details appear below the map</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">2. Test Map Display:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Select a location using the picker above</li>
              <li>Verify that the map displays with the correct marker</li>
              <li>Click "Get Directions" to open Google Maps directions</li>
              <li>Click "View on Maps" to open the location in Google Maps</li>
              <li>Click the marker to see the info window</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium mb-2">3. Quick Tests:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Use the quick test location buttons above</li>
              <li>Test with different locations to verify functionality</li>
              <li>Check browser console for any errors</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
