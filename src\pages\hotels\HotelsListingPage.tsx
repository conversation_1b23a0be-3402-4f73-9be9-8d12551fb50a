import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import HotelCard from "@/components/hotels/HotelCard";
import { Plus } from "lucide-react";
import { Tables } from "@/integrations/supabase/types";

type Hotel = Tables<"hotels">;
type RoomType = Tables<"room_types">;

interface HotelWithDetails extends Hotel {
  room_types: RoomType[];
  avg_rating?: number;
  review_count?: number;
  min_price?: number;
}

const HotelsListingPage = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");

  const {
    data: hotels,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["hotels", searchTerm],
    queryFn: async () => {
      let query = supabase
        .from("hotels")
        .select(
          `
          *,
          room_types (*)
        `
        )
        .eq("status", "approved")
        .eq("is_active", true);

      if (searchTerm) {
        query = query.or(
          `title.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%`
        );
      }

      const { data: hotelsData, error: hotelsError } = await query;

      if (hotelsError) throw hotelsError;

      // Fetch reviews and calculate ratings for each hotel
      const hotelsWithDetails = await Promise.all(
        (hotelsData || []).map(async (hotel) => {
          const { data: reviewsData } = await supabase
            .from("reviews")
            .select("rating")
            .eq("hotel_id", hotel.id);

          const avg_rating =
            reviewsData && reviewsData.length > 0
              ? reviewsData.reduce((sum, review) => sum + review.rating, 0) /
                reviewsData.length
              : 0;

          const min_price =
            hotel.room_types && hotel.room_types.length > 0
              ? Math.min(
                  ...hotel.room_types.map((rt: RoomType) => rt.base_price)
                )
              : 0;

          return {
            ...hotel,
            avg_rating,
            review_count: reviewsData?.length || 0,
            min_price,
          };
        })
      );

      return hotelsWithDetails;
    },
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Hotels</h1>
            <p className="text-gray-600 mt-2">
              Discover comfortable hotels with excellent amenities
            </p>
          </div>

          {user && (
            <Button asChild className="bg-accent hover:bg-accent/90">
              <Link to="/hotels/create">
                <Plus className="w-4 h-4 mr-2" />
                List Your Hotel
              </Link>
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="max-w-md">
            <input
              type="text"
              placeholder="Search hotels by name or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
            />
          </div>
        </div>

        {/* Hotels Grid */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              Error loading hotels
            </h3>
            <p className="text-gray-500 mt-2">Please try again later.</p>
          </div>
        ) : hotels && hotels.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {hotels.map((hotel) => (
              <HotelCard
                key={hotel.id}
                hotel={{
                  id: hotel.id,
                  title: hotel.title,
                  location: hotel.location,
                  rating: hotel.avg_rating,
                  reviews: hotel.review_count,
                  images: hotel.images,
                  check_in_time: hotel.check_in_time,
                  check_out_time: hotel.check_out_time,
                  amenities: hotel.amenities,
                  min_price: hotel.min_price,
                  is_dummy: hotel.is_dummy,
                }}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              No hotels found
            </h3>
            <p className="text-gray-500 mt-2">
              {searchTerm
                ? "Try adjusting your search terms."
                : "Be the first to list a hotel on our platform!"}
            </p>
            {user && !searchTerm && (
              <Button asChild className="mt-4 bg-accent hover:bg-accent/90">
                <Link to="/hotels/create">
                  <Plus className="w-4 h-4 mr-2" />
                  List Your Hotel
                </Link>
              </Button>
            )}
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default HotelsListingPage;
