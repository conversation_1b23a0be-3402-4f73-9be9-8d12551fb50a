# Booking & Payment Flows Test Cases

## Test Category: Booking & Payment Flows
**Priority**: Critical
**Estimated Time**: 70 minutes

---

## TC-PAY-001: Stripe Payment Processing
**Objective**: Verify Stripe payment integration works correctly

### Test Steps:
1. Complete any booking flow to payment page
2. Select "Credit/Debit Card" payment method
3. Enter test card details:
   - Card Number: ****************
   - Expiry: 12/25
   - CVC: 123
   - Name: Test User
4. Complete payment process
5. Verify payment success page
6. Check booking confirmation email
7. Verify booking status in dashboard
8. Test failed payment scenario:
   - Use declined card: ****************
   - Verify error handling

### Expected Results:
- Stripe payment form loads correctly
- Valid card completes payment successfully
- Payment success page displays
- Booking confirmation is sent
- Booking status updates to "confirmed"
- Failed payments show appropriate errors
- User is not charged for failed payments

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-002: Wave Money Payment Processing
**Objective**: Verify Wave Money payment integration

### Test Steps:
1. Complete any booking flow to payment page
2. Select "Mobile Money" payment method
3. Enter mobile money details:
   - Phone Number: +**********
   - Provider: Orange/Africell
4. Complete payment process
5. Verify redirect to Wave payment page
6. Complete payment on Wave platform
7. Verify return to success page
8. Check booking confirmation
9. Test payment cancellation scenario

### Expected Results:
- Wave payment option is available
- Redirect to Wave platform works
- Payment can be completed on Wave
- Return redirect functions properly
- Booking is confirmed after payment
- Cancellation returns to booking page
- Payment status is tracked correctly

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-003: Payment Amount Calculations
**Objective**: Verify payment amounts are calculated correctly

### Test Steps:
1. Test property booking calculations:
   - Base price per night
   - Multiple nights calculation
   - Platform fee (15%)
   - Tax calculations (if applicable)
2. Test car rental calculations:
   - Daily rate
   - Multiple days
   - Insurance costs
   - Platform fee
3. Test hotel booking calculations:
   - Room rate per night
   - Multiple rooms
   - Multiple nights
   - Platform fee
4. Verify all calculations in booking summary
5. Compare with payment amount

### Expected Results:
- Base prices are calculated correctly
- Multi-day/night calculations are accurate
- Platform fee (15%) is applied correctly
- Insurance costs are added properly
- Total amounts match payment requests
- All fees are clearly itemized
- Calculations are consistent across booking types

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-004: Booking Confirmation Process
**Objective**: Verify booking confirmation workflow

### Test Steps:
1. Complete a successful payment
2. Verify immediate confirmation:
   - Success page displays
   - Booking ID is generated
   - Confirmation details are shown
3. Check email confirmation:
   - Email is sent to user
   - Email contains booking details
   - Email has correct information
4. Verify booking appears in user dashboard
5. Check host notification (if applicable)
6. Verify booking status in admin panel

### Expected Results:
- Success page shows immediately after payment
- Booking ID is unique and trackable
- Confirmation email is sent promptly
- Email contains all relevant details
- Booking appears in user's booking history
- Host receives booking notification
- Admin can see the booking

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-005: Payment Failure Handling
**Objective**: Verify payment failure scenarios are handled properly

### Test Steps:
1. Test various payment failure scenarios:
   - Insufficient funds (card: ****************)
   - Expired card (card: ****************)
   - Incorrect CVC (card: ****************)
   - Network timeout
2. For each failure:
   - Verify appropriate error message
   - Check booking status remains "pending"
   - Verify user can retry payment
   - Check no duplicate bookings are created
3. Test payment timeout scenarios
4. Verify booking cleanup for failed payments

### Expected Results:
- Each failure type shows specific error message
- Booking status remains pending until payment
- Users can retry payment without issues
- No duplicate bookings are created
- Failed payments don't reserve inventory
- Timeout scenarios are handled gracefully

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-006: Booking Status Management
**Objective**: Verify booking status transitions work correctly

### Test Steps:
1. Create booking and track status changes:
   - Initial: "pending"
   - After payment: "confirmed"
   - After check-in: "active" (if applicable)
   - After completion: "completed"
2. Test status-based functionality:
   - Pending bookings can be cancelled
   - Confirmed bookings have restrictions
   - Completed bookings can be reviewed
3. Verify status visibility:
   - User sees correct status
   - Host sees correct status
   - Admin sees correct status
4. Test status-based notifications

### Expected Results:
- Status transitions occur automatically
- Status-based rules are enforced
- All parties see consistent status
- Notifications are sent on status changes
- Status history is maintained
- Status affects available actions

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-007: Refund Processing
**Objective**: Verify refund processing works correctly

### Test Steps:
1. Create and confirm a booking
2. Initiate cancellation within refund period
3. Verify refund calculation:
   - Check cancellation policy
   - Calculate refund amount
   - Verify platform fee handling
4. Process refund through payment system
5. Verify refund confirmation
6. Check refund appears in user account
7. Test partial refund scenarios
8. Test no-refund scenarios

### Expected Results:
- Refund amount is calculated correctly
- Cancellation policy is applied properly
- Refunds are processed through original payment method
- Refund confirmation is provided
- Refund status is tracked
- Partial refunds work correctly
- No-refund policies are enforced

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-008: Platform Fee Management
**Objective**: Verify 15% platform fee is handled correctly

### Test Steps:
1. Complete various bookings and verify:
   - Platform fee is 15% of booking amount
   - Fee is clearly displayed to user
   - Fee is included in total payment
2. Verify host payout calculations:
   - Host receives 85% of booking amount
   - Platform retains 15% fee
   - Calculations are accurate
3. Check platform earnings tracking:
   - Fees are recorded in platform_earnings table
   - Earnings reports are accurate
   - Fee breakdown is available
4. Test fee calculations with discounts/promotions

### Expected Results:
- Platform fee is consistently 15%
- Fee is transparently displayed
- Host payouts are calculated correctly
- Platform earnings are tracked accurately
- Fee applies to base amount before taxes
- Discounts don't affect platform fee percentage

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-009: Payment Security
**Objective**: Verify payment security measures are in place

### Test Steps:
1. Verify SSL/TLS encryption:
   - Payment pages use HTTPS
   - Certificate is valid
   - No mixed content warnings
2. Test payment data handling:
   - Card details are not stored locally
   - Payment tokens are used properly
   - PCI compliance measures
3. Verify payment form security:
   - Form validation prevents injection
   - CSRF protection is active
   - Rate limiting on payment attempts
4. Test session security during payment
5. Verify payment logs don't contain sensitive data

### Expected Results:
- All payment pages use HTTPS
- Card data is handled securely
- Payment tokens are used instead of raw data
- Forms are protected against attacks
- Sessions are secure during payment
- Logs don't contain sensitive information
- PCI compliance requirements are met

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-PAY-010: Cross-Platform Payment Consistency
**Objective**: Verify payment works consistently across devices

### Test Steps:
1. Test payment flow on different devices:
   - Desktop browser
   - Mobile browser
   - Tablet browser
2. Test different browsers:
   - Chrome
   - Firefox
   - Safari
   - Edge
3. Verify payment form responsiveness
4. Test payment completion on each platform
5. Verify confirmation emails work on all platforms
6. Test payment retry functionality

### Expected Results:
- Payment forms are responsive
- Payment works on all tested browsers
- Mobile payment experience is optimized
- Confirmation process is consistent
- Email confirmations work everywhere
- Payment retry works on all platforms
- No platform-specific payment issues

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
