import { useState } from "react";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { But<PERSON> } from "@/components/ui/button";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import SearchFilters from "@/components/properties/SearchFilters";
import PropertyCard from "@/components/properties/PropertyCard";
import { Plus } from "lucide-react";

const ListingsPage = () => {
  const { user } = useAuth();
  const [sortOption, setSortOption] = useState("recommended");
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    minPrice: undefined as number | undefined,
    maxPrice: undefined as number | undefined,
    propertyType: "",
    bedrooms: "",
    amenities: [] as string[],
  });

  const { data: allProperties, isLoading } = useQuery({
    queryKey: ["properties", sortOption, filters],
    queryFn: async () => {
      let query = supabase
        .from("properties")
        .select("*")
        .eq("status", "approved")
        .eq("is_active", true);

      // Apply price filters
      if (filters.minPrice) {
        query = query.gte("price", filters.minPrice);
      }
      if (filters.maxPrice) {
        query = query.lte("price", filters.maxPrice);
      }

      // Apply property type filter
      if (filters.propertyType) {
        query = query.eq("property_type", filters.propertyType);
      }

      // Apply bedrooms filter
      if (filters.bedrooms) {
        query = query.gte("beds", parseInt(filters.bedrooms));
      }

      // Apply sorting
      if (sortOption === "price-low") {
        query = query.order("price", { ascending: true });
      } else if (sortOption === "price-high") {
        query = query.order("price", { ascending: false });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      const { data, error } = await query;
      if (error) throw error;

      // Fetch reviews and calculate ratings for each property
      const propertiesWithReviews = await Promise.all(
        (data || []).map(async (property) => {
          const { data: reviewsData } = await supabase
            .from("reviews")
            .select("rating")
            .eq("property_id", property.id);

          const avgRating =
            reviewsData && reviewsData.length > 0
              ? reviewsData.reduce((sum, review) => sum + review.rating, 0) /
                reviewsData.length
              : 0;

          return {
            id: property.id,
            title: property.title || "Untitled Property",
            location: property.location || "Location not specified",
            price: Number(property.price) || 0,
            rating: avgRating,
            reviews: reviewsData?.length || 0,
            image: property.images?.[0] || "/placeholder.svg",
            beds: Number(property.beds) || 0,
            baths: Number(property.baths) || 0,
            isSuperHost: false,
            is_dummy: property.is_dummy,
          };
        })
      );

      return propertiesWithReviews;
    },
  });

  // Filter properties based on search term
  const properties = allProperties?.filter(
    (property) =>
      (property.title || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
      (property.location || "").toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-3xl font-bold text-brown mb-4 md:mb-0">
            Find your perfect stay
          </h1>

          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <label className="mr-2 text-gray-600">Sort by:</label>
              <select
                value={sortOption}
                onChange={(e) => setSortOption(e.target.value)}
                className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary"
              >
                <option value="recommended">Recommended</option>
                <option value="price-low">Price (Low to High)</option>
                <option value="price-high">Price (High to Low)</option>
              </select>
            </div>

            {user && (
              <Link to="/listings/create">
                <Button className="bg-accent hover:bg-accent/90 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  List Property
                </Button>
              </Link>
            )}
          </div>
        </div>

        <SearchFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filters={filters}
          onFiltersChange={setFilters}
        />

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent"></div>
          </div>
        ) : properties && properties.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {properties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              No properties found
            </h3>
            <p className="text-gray-500 mt-2">
              Try adjusting your filters or check back later.
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default ListingsPage;
