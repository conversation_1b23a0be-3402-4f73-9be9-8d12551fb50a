import { useState, useEffect } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

interface FeatureSelectorProps {
  type: "property" | "car";
  selectedFeatures: string[];
  onFeaturesChange: (features: string[]) => void;
}

const PROPERTY_FEATURES = [
  "Kitchen",
  "Wi-Fi",
  "Air Conditioning",
  "Heating",
  "Pool",
  "Gym",
  "Parking",
  "Security",
  "Balcony",
  "Garden",
  "Laundry",
  "TV",
  "Fireplace",
  "Hot Tub",
  "Beach Access",
  "Mountain View",
  "City View",
  "Pet Friendly",
  "Wheelchair Accessible",
  "Smoking Allowed",
];

const CAR_FEATURES = [
  "Air Conditioning",
  "Bluetooth",
  "GPS Navigation",
  "Backup Camera",
  "Parking Sensors",
  "Leather Seats",
  "Sunroof",
  "Heated Seats",
  "USB Charging",
  "Apple CarPlay",
  "Android Auto",
  "Premium Sound System",
  "Lane Assist",
  "Cruise Control",
  "Keyless Entry",
  "Push Start",
  "Tinted Windows",
  "Roof Rack",
  "Tow Hitch",
  "Sport Mode",
];

const FeatureSelector = ({
  type,
  selectedFeatures,
  onFeaturesChange,
}: FeatureSelectorProps) => {
  const [showOthers, setShowOthers] = useState(false);
  const [customFeature, setCustomFeature] = useState("");
  const [customFeatures, setCustomFeatures] = useState<string[]>([]);

  const predefinedFeatures =
    type === "property" ? PROPERTY_FEATURES : CAR_FEATURES;

  // Initialize custom features from selectedFeatures when component mounts or selectedFeatures changes
  useEffect(() => {
    const customFeats = selectedFeatures.filter(
      (f) => !predefinedFeatures.includes(f)
    );
    setCustomFeatures(customFeats);
    setShowOthers(customFeats.length > 0);
  }, [selectedFeatures, predefinedFeatures]);

  const handleFeatureToggle = (feature: string, checked: boolean) => {
    if (checked) {
      onFeaturesChange([...selectedFeatures, feature]);
    } else {
      onFeaturesChange(selectedFeatures.filter((f) => f !== feature));
    }
  };

  const handleOthersToggle = (checked: boolean) => {
    setShowOthers(checked);
    if (!checked) {
      // Remove all custom features when unchecking "Others"
      const predefinedSelected = selectedFeatures.filter((f) =>
        predefinedFeatures.includes(f)
      );
      onFeaturesChange(predefinedSelected);
      setCustomFeatures([]);
    }
  };

  const addCustomFeature = () => {
    if (
      customFeature.trim() &&
      !customFeatures.includes(customFeature.trim())
    ) {
      const newCustomFeature = customFeature.trim();
      const newCustomFeatures = [...customFeatures, newCustomFeature];
      setCustomFeatures(newCustomFeatures);
      onFeaturesChange([...selectedFeatures, newCustomFeature]);
      setCustomFeature("");
    }
  };

  const removeCustomFeature = (feature: string) => {
    const newCustomFeatures = customFeatures.filter((f) => f !== feature);
    setCustomFeatures(newCustomFeatures);
    onFeaturesChange(selectedFeatures.filter((f) => f !== feature));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addCustomFeature();
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-semibold">
          {type === "property" ? "Property Features" : "Car Features"}
        </Label>
        <p className="text-sm text-muted-foreground mb-4">
          Select all features that apply to your {type}
        </p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {predefinedFeatures.map((feature) => (
          <div key={feature} className="flex items-center space-x-2">
            <Checkbox
              id={feature}
              checked={selectedFeatures.includes(feature)}
              onCheckedChange={(checked) =>
                handleFeatureToggle(feature, checked as boolean)
              }
            />
            <Label
              htmlFor={feature}
              className="text-sm font-normal cursor-pointer"
            >
              {feature}
            </Label>
          </div>
        ))}

        <div className="flex items-center space-x-2">
          <Checkbox
            id="others"
            checked={showOthers}
            onCheckedChange={handleOthersToggle}
          />
          <Label
            htmlFor="others"
            className="text-sm font-normal cursor-pointer"
          >
            Others
          </Label>
        </div>
      </div>

      {showOthers && (
        <div className="space-y-3 p-4 border rounded-lg bg-gray-50">
          <Label className="text-sm font-medium">Add Custom Features</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter a custom feature..."
              value={customFeature}
              onChange={(e) => setCustomFeature(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button
              type="button"
              onClick={addCustomFeature}
              size="sm"
              className="bg-accent hover:bg-accent/90"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {customFeatures.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Custom Features:</Label>
              <div className="flex flex-wrap gap-2">
                {customFeatures.map((feature) => (
                  <div
                    key={feature}
                    className="flex items-center gap-1 px-2 py-1 bg-accent text-white rounded-full text-sm"
                  >
                    <span>{feature}</span>
                    <button
                      type="button"
                      onClick={() => removeCustomFeature(feature)}
                      className="hover:bg-accent/80 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FeatureSelector;
