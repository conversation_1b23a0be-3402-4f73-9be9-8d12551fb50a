import { useState, useEffect } from "react";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface Reservation {
  id: string;
  type: "property" | "car";
  title: string;
  imageUrl: string | null;
  guestName: string;
  guestAvatar: string | null;
  startDate: string | null;
  endDate: string | null;
  amount: number;
  status: string;
  paymentStatus: string;
  createdAt: string;
}

const HostReservations = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchReservations();
  }, [user]);

  const fetchReservations = async () => {
    if (!user) return;

    setIsLoading(true);

    try {
      // Fetch property bookings for this host's properties
      const { data: propertyBookings, error: propertyError } = await supabase
        .from("bookings")
        .select(
          `
          *,
          properties!inner(*)
        `
        )
        .eq("properties.owner_id", user.id)
        .order("created_at", { ascending: false });

      if (propertyError) throw propertyError;

      // Create a simplified object for display
      const transformedPropertyBookings = propertyBookings.map((booking) => {
        const guestName = "Guest";
        const guestAvatar = null;

        return {
          id: booking.id,
          type: "property" as const,
          title: booking.properties?.title || "Unknown property",
          imageUrl: booking.properties?.images?.[0] || null,
          guestName,
          guestAvatar,
          startDate: booking.check_in,
          endDate: booking.check_out,
          amount: booking.total_price,
          status: booking.status,
          paymentStatus: booking.payment_status || "unknown",
          createdAt: booking.created_at,
        };
      });

      // Fetch car bookings for this host's cars
      const { data: carBookings, error: carError } = await supabase
        .from("car_bookings")
        .select(
          `
          *,
          cars!inner(*)
        `
        )
        .eq("cars.owner_id", user.id)
        .order("created_at", { ascending: false });

      if (carError) throw carError;

      // Create a simplified object for display
      const transformedCarBookings = carBookings.map((booking) => {
        const guestName = "Guest";
        const guestAvatar = null;

        return {
          id: booking.id,
          type: "car" as const,
          title:
            booking.cars?.title ||
            `${booking.cars?.make} ${booking.cars?.model}`,
          imageUrl: booking.cars?.images?.[0] || null,
          guestName,
          guestAvatar,
          startDate: booking.start_date,
          endDate: booking.end_date,
          amount: booking.total_price,
          status: booking.status,
          paymentStatus: "completed", // Car bookings do not have payment_status
          createdAt: booking.created_at,
        };
      });

      // Combine and sort all bookings by date
      const allBookings = [
        ...transformedPropertyBookings,
        ...transformedCarBookings,
      ].sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setReservations(allBookings);
    } catch (error) {
      console.error("Error fetching reservations:", error);
      toast({
        title: "Error fetching reservations",
        description: "There was a problem loading your reservations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-muted-teal/20 text-muted-teal";
      case "pending":
        return "bg-soft-orange/20 text-rich-brown";
      case "cancelled":
        return "bg-destructive/20 text-destructive";
      case "modified":
        return "bg-bright-aqua/20 text-muted-teal";
      default:
        return "bg-muted text-muted-foreground";
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Your Reservations</h1>
      {reservations.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No reservations found.</p>
        </div>
      ) : (
        <Table>
          <TableCaption>A list of your recent reservations</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Guest</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell>{reservation.type}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    {reservation.imageUrl && (
                      <img
                        src={reservation.imageUrl}
                        alt={reservation.title}
                        className="w-12 h-12 rounded object-cover"
                      />
                    )}
                    <span>{reservation.title}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar>
                      <AvatarImage src={reservation.guestAvatar || ""} />
                      <AvatarFallback>
                        {reservation.guestName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span>{reservation.guestName}</span>
                  </div>
                </TableCell>
                <TableCell>{formatDate(reservation.startDate)}</TableCell>
                <TableCell>{formatDate(reservation.endDate)}</TableCell>
                <TableCell>${reservation.amount}</TableCell>
                <TableCell>
                  <Badge className={getStatusBadgeClass(reservation.status)}>
                    {reservation.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    className={getStatusBadgeClass(reservation.paymentStatus)}
                  >
                    {reservation.paymentStatus}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
};

export default HostReservations;
