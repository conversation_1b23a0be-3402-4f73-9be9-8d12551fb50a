# Car Rental System Test Cases

## Test Category: Car Rental System
**Priority**: Critical
**Estimated Time**: 50 minutes

---

## TC-CAR-001: Create Car Listing
**Objective**: Verify hosts can create car rental listings

### Test Steps:
1. <PERSON><PERSON> as car owner/host
2. Navigate to "Add Car" or "Create Car Listing" page
3. Fill in car details:
   - Make: "Toyota"
   - Model: "Camry"
   - Year: "2022"
   - Car Type: "Sedan"
   - Daily Rate: "75"
   - Location: "Banjul, The Gambia"
   - Description: "Reliable sedan perfect for city driving"
4. Upload car images (exterior, interior, documents)
5. Set car features (AC, GPS, etc.)
6. Set availability calendar
7. Add insurance options
8. Submit car listing

### Expected Results:
- Form accepts all car details
- Image upload works for multiple categories
- Features selection functions properly
- Availability calendar is interactive
- Insurance options can be configured
- Car listing is created with pending status

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-002: Car Booking Flow
**Objective**: Verify users can book cars successfully

### Test Steps:
1. Navigate to car listings page
2. Select an available car
3. Choose rental dates:
   - Pick-up date: Tomorrow
   - Return date: 3 days later
4. Select pick-up and drop-off locations
5. Choose insurance options (if available)
6. Add driver information
7. Review booking summary
8. Proceed to payment
9. Complete booking with test payment
10. Verify booking confirmation

### Expected Results:
- Car availability is checked for selected dates
- Location selection works properly
- Insurance options are presented
- Booking summary shows correct calculations
- Payment process completes successfully
- Booking confirmation is received
- Calendar is updated with booking

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-003: Car Availability Management
**Objective**: Verify car availability system works correctly

### Test Steps:
1. Login as car owner
2. Navigate to car management dashboard
3. Select a car to manage availability
4. Block specific dates as unavailable
5. Set different pricing for peak dates
6. Save availability changes
7. Test from customer perspective:
   - Try to book blocked dates
   - Verify pricing changes on peak dates
   - Book available dates
8. Verify booking updates availability

### Expected Results:
- Availability calendar is editable
- Blocked dates cannot be booked
- Pricing changes are reflected
- Bookings automatically block dates
- Availability updates in real-time
- Past dates are automatically blocked

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-004: Car Insurance Options
**Objective**: Verify insurance selection and pricing

### Test Steps:
1. Navigate to car booking page
2. Select a car for rental
3. Choose rental dates
4. Review insurance options:
   - Basic coverage
   - Comprehensive coverage
   - Premium coverage
5. Select different insurance levels
6. Verify pricing updates with insurance
7. Complete booking with insurance
8. Verify insurance details in confirmation

### Expected Results:
- Insurance options are clearly displayed
- Insurance descriptions are informative
- Pricing updates correctly with insurance
- Insurance selection is saved with booking
- Insurance details appear in confirmation
- Insurance costs are itemized

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-005: Car Search and Filtering
**Objective**: Verify car search and filter functionality

### Test Steps:
1. Navigate to car listings page
2. Test search functionality:
   - Search by location
   - Search by car make/model
3. Test filtering options:
   - Filter by car type (sedan, SUV, etc.)
   - Filter by price range
   - Filter by features (AC, GPS, etc.)
   - Filter by availability dates
4. Test sorting options:
   - Sort by price (low to high, high to low)
   - Sort by rating
   - Sort by distance
5. Verify results accuracy

### Expected Results:
- Search returns relevant cars
- Filters work independently and combined
- Sorting functions correctly
- Results update dynamically
- No results message when appropriate
- Filter counts are accurate

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-006: Car Detail Page
**Objective**: Verify car detail page displays all information

### Test Steps:
1. Navigate to car listings
2. Click on a car to view details
3. Verify car detail page shows:
   - Car specifications (make, model, year)
   - Image gallery
   - Features and amenities
   - Pricing information
   - Location and pickup details
   - Insurance options
   - Host/owner information
   - Reviews and ratings
   - Booking widget
4. Test image gallery functionality
5. Test booking widget interaction

### Expected Results:
- All car information displays correctly
- Image gallery functions properly
- Pricing is clear and accurate
- Location information is detailed
- Booking widget is functional
- Host contact options are available
- Page is mobile responsive

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-007: Car Booking Modifications
**Objective**: Verify users can modify car bookings

### Test Steps:
1. Create a car booking (future dates)
2. Login and navigate to booking management
3. Select the car booking to modify
4. Test modification options:
   - Change rental dates
   - Modify pickup/dropoff locations
   - Add/remove insurance
   - Update driver information
5. Calculate price differences
6. Process payment adjustment (if needed)
7. Verify booking update confirmation

### Expected Results:
- Booking modifications are allowed (within policy)
- Date changes check availability
- Price adjustments are calculated correctly
- Payment differences are processed
- Updated booking confirmation is sent
- Calendar reflects changes

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-008: Car Booking Cancellation
**Objective**: Verify car booking cancellation process

### Test Steps:
1. Create a car booking (future dates)
2. Navigate to booking management
3. Select booking to cancel
4. Review cancellation policy
5. Initiate cancellation process
6. Confirm cancellation
7. Verify refund calculation
8. Check booking status update
9. Verify calendar availability update

### Expected Results:
- Cancellation policy is clearly displayed
- Refund amount is calculated correctly
- Cancellation confirmation is provided
- Booking status updates to cancelled
- Calendar dates become available again
- Refund is processed according to policy

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-009: Car Owner Dashboard
**Objective**: Verify car owner management features

### Test Steps:
1. Login as car owner
2. Navigate to car owner dashboard
3. Verify dashboard displays:
   - List of owned cars
   - Booking requests/confirmations
   - Earnings summary
   - Calendar overview
   - Recent activity
4. Test car management functions:
   - Edit car details
   - Manage availability
   - View booking details
   - Respond to booking requests
5. Test earnings and payout features

### Expected Results:
- Dashboard provides comprehensive overview
- All owned cars are listed
- Booking information is accurate
- Earnings calculations are correct
- Car management functions work
- Payout requests can be submitted

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## TC-CAR-010: Car Payment Processing
**Objective**: Verify car rental payment processing

### Test Steps:
1. Complete car booking flow to payment
2. Test payment methods:
   - Credit/debit card (Stripe)
   - Mobile money (Wave)
3. Test payment scenarios:
   - Successful payment
   - Failed payment
   - Payment timeout
4. Verify payment confirmation
5. Test refund processing
6. Verify payment status updates

### Expected Results:
- Multiple payment methods work
- Successful payments complete booking
- Failed payments show appropriate errors
- Payment confirmations are sent
- Refunds process correctly
- Payment status is tracked accurately

**Status**: [ ] ✅ PASS [ ] ❌ FAIL [ ] ⚠️ PARTIAL
**Notes**: ________________________________

---

## Summary
**Total Test Cases**: 10
**Passed**: ___/10
**Failed**: ___/10
**Partial**: ___/10

**Critical Issues Found**: ________________
**Recommendations**: ________________
**Ready for Production**: [ ] Yes [ ] No
