import { Link } from "react-router-dom";
import { MapPin, Star, Bed, Bath, TestTube } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatPrice } from "@/lib/price-utils";

interface PropertyCardProps {
  property: {
    id: string;
    title: string;
    location: string;
    price: number;
    rating: number;
    reviews: number;
    image: string;
    beds: number;
    baths: number;
    isSuperHost?: boolean;
    is_dummy?: boolean;
  };
}

const PropertyCard = ({ property }: PropertyCardProps) => {
  const {
    id,
    title,
    location,
    price,
    rating,
    reviews,
    image,
    beds,
    baths,
    isSuperHost = false,
  } = property;

  const displayRating = rating || 0;
  const displayReviews = reviews || 0;
  const displayPrice = formatPrice(price);

  return (
    <Link to={`/listings/${id}`} className="group">
      <div className="bg-card rounded-lg shadow-sm overflow-hidden transition-shadow hover:shadow-md h-full flex flex-col">
        {/* Image */}
        <div className="aspect-[4/3] relative overflow-hidden">
          <img
            src={image || "/placeholder.svg"}
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          {isSuperHost && (
            <div className="absolute top-2 left-2 px-2 py-1 bg-card text-xs font-medium rounded-full shadow-sm">
              Superhost
            </div>
          )}
          {property.is_dummy && (
            <div className="absolute top-2 right-2 px-2 py-1 bg-soft-orange text-white text-xs font-medium rounded-full shadow-md flex items-center gap-1">
              <TestTube className="w-3 h-3" />
              Display Only
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4 flex-1 flex flex-col">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-foreground truncate" title={title}>
              {title}
            </h3>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-soft-orange" />
              <span className="ml-1 text-sm text-foreground">
                {displayRating.toFixed(1)}
              </span>
              <span className="ml-1 text-xs text-muted-foreground">
                ({displayReviews})
              </span>
            </div>
          </div>

          <div className="flex items-center text-sm text-muted-foreground mb-2">
            <MapPin className="h-3 w-3 mr-1" />
            <span className="truncate" title={location}>
              {location}
            </span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground mb-3 flex-1">
            <Bed className="h-3 w-3 mr-1" />
            <span>
              {beds} {beds === 1 ? "bed" : "beds"}
            </span>
            <span className="mx-1">·</span>
            <Bath className="h-3 w-3 mr-1" />
            <span>
              {baths} {baths === 1 ? "bath" : "baths"}
            </span>
          </div>

          <div className="font-medium mt-auto">
            <span className="text-foreground">${displayPrice}</span>
            <span className="text-muted-foreground"> / night</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PropertyCard;
